import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { analyticsControllers } from '../../controllers/analytics/analytic.controller';

export const analyticsRoute = Router();

analyticsRoute.get(
  '/booking-graph',
  secure,
  analyticsControllers.BookingGraphAnalytics
);
analyticsRoute.get(
  '/booking-stats',
  secure,
  analyticsControllers.BookingStatsAnalytics
);
analyticsRoute.get(
  '/dashboard',
  secure,
  analyticsControllers.DashboardAnalytics
);
analyticsRoute.get(
  '/feedback/dashboard',
  secure,
  analyticsControllers.FeedbackAnalytics
);
analyticsRoute.get(
  '/staff-referral',
  secure,
  analyticsControllers.StaffReferralAnalytics
);
analyticsRoute.get(
  '/transactions',
  secure,
  analyticsControllers.TransactionAnalytics
);
analyticsRoute.get(
  '/discounts',
  secure,
  analyticsControllers.DiscountAnalytics
);
