/*
  Warnings:

  - The primary key for the `Update` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `name` on the `Update` table. All the data in the column will be lost.
  - You are about to drop the column `package_id` on the `Update` table. All the data in the column will be lost.
  - You are about to drop the column `referral_date` on the `external_referrals` table. All the data in the column will be lost.
  - Added the required column `identifier` to the `Update` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `Update` table without a default value. This is not possible if the table is not empty.
  - Added the required column `referral_id` to the `external_referrals` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON>reateEnum
CREATE TYPE "UpdateType" AS ENUM ('PATIENT_REFERRAL', 'PACKAGE_BOOKING', 'REFERRING_ENTITY', 'REWARD', 'PACKAGE');

-- AlterTable
ALTER TABLE "Update" DROP CONSTRAINT "Update_pkey",
DROP COLUMN "name",
DROP COLUMN "package_id",
ADD COLUMN     "identifier" TEXT NOT NULL,
ADD COLUMN     "type" "UpdateType" NOT NULL,
ADD COLUMN     "update_id" SERIAL NOT NULL,
ADD CONSTRAINT "Update_pkey" PRIMARY KEY ("update_id");

-- AlterTable
ALTER TABLE "external_referrals" DROP COLUMN "referral_date",
ADD COLUMN     "referral_id" TEXT NOT NULL;
