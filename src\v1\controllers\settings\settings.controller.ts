import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { settingsService } from '../../services/settings';

const ListSettingsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(settingsService.getAllSettings, undefined, res, staffId);
};

const CreateSettingHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(settingsService.createSetting, req.body, res, staffId);
};

const UpdateSettingHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(settingsService.updateSetting, req.body, res, staffId);
};

const DeleteSettingHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const id = parseInt(req.params.id);
  controllerOperations(settingsService.deleteSetting, id, res, staffId);
};

const GetAdminNotificationEmailsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    settingsService.getAdminNotificationEmails,
    undefined,
    res
  );
};

const UpdateAdminNotificationEmailsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { emails } = req.body;
  controllerOperations(
    settingsService.updateAdminNotificationEmails,
    emails,
    res,
    staffId
  );
};

export const settingsControllers = {
  ListSettingsHandler,
  CreateSettingHandler,
  UpdateSettingHandler,
  DeleteSettingHandler,
  GetAdminNotificationEmailsHandler,
  UpdateAdminNotificationEmailsHandler,
};
