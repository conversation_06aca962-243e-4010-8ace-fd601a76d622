import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import {
  transactionStatus,
  transactionType,
  createDateFilter,
} from '../../utils/util';

export const transactionService = {
  getAllTransactions: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_VIEW);

    // Parse pagination parameters with defaults
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;

    // Parse filter parameters
    const status = query.status;
    const type = query.type;
    const search = query.search as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;

    // Validate filter parameters
    if (status && !transactionStatus.includes(status.toUpperCase())) {
      throw new HttpError('Invalid transaction status', 400);
    }
    if (type && !transactionType.includes(type.toUpperCase())) {
      throw new HttpError('Invalid transaction type', 400);
    }

    // Create date filter using the reusable function
    const dateFilter = createDateFilter(startDate, endDate);

    // Build where clause with all filters
    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { reference: { contains: search, mode: 'insensitive' } },
              { mode: { contains: search, mode: 'insensitive' } },
              {
                user: {
                  OR: [
                    { emailAddress: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } },
                    { uhid: { contains: search, mode: 'insensitive' } },
                  ],
                },
              },
              {
                staff: {
                  OR: [
                    { email: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } },
                    { fullName: { contains: search, mode: 'insensitive' } },
                    { location:{ name:{ contains: search, mode: 'insensitive' }} },
                  ],
                },
              },
            ],
          }
        : {}),
      ...(status ? { status: status.toUpperCase() } : {}),
      ...(type ? { type: type.toUpperCase() } : {}),
      ...dateFilter,
    };

    const [transactions, totalPages, totalCount] = await db.$transaction([
      db.transaction.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        include: {
          user: true,
          staff: {
            include: {
              location: true,
            }
          },
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.transaction.count({
        where: whereClause,
      }),
      db.transaction.count(),
    ]);

    return {
      transactions: transactions,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
      // filters: {
      //   status: status || null,
      //   type: type || null,
      //   search: search || null,
      //   dateRange:
      //     startDate || endDate
      //       ? {
      //           startDate: startDate || null,
      //           endDate: endDate || null,
      //         }
      //       : null,
      // },
    };
  },
};
