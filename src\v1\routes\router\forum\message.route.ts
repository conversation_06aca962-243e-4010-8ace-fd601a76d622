import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { ForumMessageController } from '../../../controllers/forum/messageController';
import {
  forumUpload,
  handleMulterError,
} from '../../../utils/upload/forumUpload';

export const forumMessageRoute = Router();

// Create a new message
forumMessageRoute.post('/create', secure, ForumMessageController.createMessage);

// Create a new message with file attachments
forumMessageRoute.post(
  '/create-with-files',
  secure,
  forumUpload.array('files', 5),
  handleMulterError,
  ForumMessageController.createMessageWithFiles
);

// Get message by ID
forumMessageRoute.get('/:messageId', secure, ForumMessageController.getMessage);

// Get messages in a channel
forumMessageRoute.get(
  '/channel/:channelId',
  secure,
  ForumMessageController.getChannelMessages
);

// Update message
forumMessageRoute.patch(
  '/:messageId',
  secure,
  ForumMessageController.updateMessage
);

// Delete message
forumMessageRoute.delete(
  '/:messageId',
  secure,
  ForumMessageController.deleteMessage
);

// Add reaction to message
forumMessageRoute.post(
  '/:messageId/reactions',
  secure,
  ForumMessageController.addReaction
);

// Remove reaction from message
forumMessageRoute.delete(
  '/:messageId/reactions',
  secure,
  ForumMessageController.removeReaction
);

// Search messages in a channel
forumMessageRoute.get(
  '/channel/:channelId/search',
  secure,
  ForumMessageController.searchMessages
);

// Get message thread (replies)
forumMessageRoute.get(
  '/:messageId/thread',
  secure,
  ForumMessageController.getMessageThread
);
