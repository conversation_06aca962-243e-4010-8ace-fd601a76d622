import { Router } from 'express';
import { bookingControllers } from '../../controllers/booking/packageBooking.controller';
import { priceModifierControllers } from '../../controllers/discount/discount.controller';
import { secure } from '../../middleware/auth';

export const bookingRoute = Router();

bookingRoute.post('/book-package', bookingControllers.PackageBookingHandler);
bookingRoute.get(
  '/package-booking/:bookingId',
  bookingControllers.SinglePackageHandler
);

bookingRoute.get(
  '/list-all-package-booking',
  secure,
  bookingControllers.ListAllPackageBookingHandler
);

bookingRoute.patch(
  '/complete-booking',
  bookingControllers.CompletePackageBookingHandler
);

bookingRoute.patch(
  '/update-booking',
  secure,
  bookingControllers.UpdatePackageBookingHandler
);

bookingRoute.delete(
  '/remove-package-booking/:id',
  bookingControllers.DeletePackageBookingHandler
);

//Price Modifiers
bookingRoute.post(
  '/verify-price-modifier',
  priceModifierControllers.VerifyPriceModifierHandler
);
