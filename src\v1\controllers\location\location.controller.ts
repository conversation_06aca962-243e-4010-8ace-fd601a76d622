import { Request, Response, NextFunction } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { locationService } from '../../services/location';

const CreateLocationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(locationService.createLocation, req.body, res, staffId);
};

const ListLocationHandler = (req: Request, res: Response) => {
  controllerOperations(locationService.getAllLocations, undefined, res);
};

const UpdateLocationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(locationService.updateLocation, req.body, res, staffId);
};

const DeleteLocationHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const locationId = Number(req.params.locationId);
  controllerOperations(
    locationService.deleteLocation,
    locationId,
    res,
    staffId
  );
};

export const locationControllers = {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ListLocation<PERSON><PERSON><PERSON>,
  UpdateLocationHandler,
  DeleteLocationHandler,
};
