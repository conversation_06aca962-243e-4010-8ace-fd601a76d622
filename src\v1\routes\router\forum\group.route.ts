import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { ForumGroupController } from '../../../controllers/forum/groupController';

export const forumGroupRoute = Router();

// Create a new forum group
forumGroupRoute.post('/create', secure, ForumGroupController.createGroup);

// Get group by ID
forumGroupRoute.get('/:groupId', secure, ForumGroupController.getGroup);

// Get user's groups
forumGroupRoute.get('/', secure, ForumGroupController.getUserGroups);

// Update group
forumGroupRoute.patch('/:groupId', secure, ForumGroupController.updateGroup);

// Delete group
forumGroupRoute.delete('/:groupId', secure, ForumGroupController.deleteGroup);

// Search groups
forumGroupRoute.get('/search/query', secure, ForumGroupController.searchGroups);

// Group member management
forumGroupRoute.post(
  '/:groupId/members',
  secure,
  ForumGroupController.addMember
);

forumGroupRoute.get(
  '/:groupId/members',
  secure,
  ForumGroupController.getGroupMembers
);

forumGroupRoute.patch(
  '/:groupId/members/:memberId',
  secure,
  ForumGroupController.updateMemberRole
);

forumGroupRoute.delete(
  '/:groupId/members/:memberId',
  secure,
  ForumGroupController.removeMember
);
