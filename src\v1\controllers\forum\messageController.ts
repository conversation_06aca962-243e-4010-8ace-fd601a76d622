import { Request, Response } from 'express';
import { ForumMessageService } from '../../services/forum/messageService';
import { FileUploadService } from '../../services/upload/fileUploadService';
import { convertMulterFiles } from '../../utils/upload/forumUpload';
import { logger } from '../../utils/logger';

export class ForumMessageController {
  // Create a new message
  static async createMessage(req: Request, res: Response) {
    try {
      const {
        content,
        messageType,
        channelId,
        parentMessageId,
        attachments,
        mentions,
      } = req.body;
      const authorId = req.Staff as unknown as number;

      if (!authorId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content || !channelId) {
        res.status(400).json({
          success: false,
          message: 'Content and channel ID are required',
        });
      }

      const message = await ForumMessageService.createMessage({
        content,
        messageType,
        channelId: parseInt(channelId),
        authorId,
        parentMessageId,
        attachments,
        mentions,
      });

      res.status(201).json({
        success: true,
        message: 'Message created successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error creating message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to create message',
      });
    }
  }

  // Get messages in a channel
  static async getChannelMessages(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const { page = 1, limit = 50 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const result = await ForumMessageService.getChannelMessages(
        parseInt(channelId),
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.messages,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting channel messages:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get channel messages',
      });
    }
  }

  // Create a new message with file attachments
  static async createMessageWithFiles(req: Request, res: Response) {
    try {
      const { content, messageType, channelId, parentMessageId, mentions } =
        req.body;
      const authorId = req.Staff as unknown as number;
      const files = req.files as Express.Multer.File[];

      if (!authorId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content || !channelId) {
        res.status(400).json({
          success: false,
          message: 'Content and channel ID are required',
        });
      }

      let attachments = undefined;

      // Handle file uploads if files are provided
      if (files && files.length > 0) {
        try {
          const fileData = convertMulterFiles(files);
          const uploadedFiles = await FileUploadService.uploadMultipleFiles(
            fileData,
            'messages',
            { useCloudinary: true }
          );

          attachments = uploadedFiles.map((file) => ({
            fileName: file.fileName,
            fileUrl: file.fileUrl,
            fileType: file.fileType,
            fileSize: file.fileSize,
          }));
        } catch (uploadError) {
          logger.error('Error uploading files:', uploadError);
          res.status(400).json({
            success: false,
            message:
              uploadError instanceof Error
                ? uploadError.message
                : 'Failed to upload files',
          });
        }
      }

      const message = await ForumMessageService.createMessage({
        content,
        messageType,
        channelId: parseInt(channelId),
        authorId,
        parentMessageId,
        attachments,
        mentions: mentions ? JSON.parse(mentions) : undefined,
      });

      res.status(201).json({
        success: true,
        message: 'Message with attachments created successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error creating message with files:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to create message',
      });
    }
  }

  // Get message by ID
  static async getMessage(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const message = await ForumMessageService.getMessageById(
        messageId,
        userId
      );

      res.status(200).json({
        success: true,
        data: message,
      });
    } catch (error) {
      logger.error('Error getting message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get message',
      });
    }
  }

  // Update message
  static async updateMessage(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const { content } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!content) {
        res.status(400).json({
          success: false,
          message: 'Content is required',
        });
      }

      const message = await ForumMessageService.updateMessage(
        messageId,
        { content },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Message updated successfully',
        data: message,
      });
    } catch (error) {
      logger.error('Error updating message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to update message',
      });
    }
  }

  // Delete message
  static async deleteMessage(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await ForumMessageService.deleteMessage(messageId, userId);

      res.status(200).json({
        success: true,
        message: 'Message deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to delete message',
      });
    }
  }

  // Add reaction to message
  static async addReaction(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const { emoji } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!emoji) {
        res.status(400).json({
          success: false,
          message: 'Emoji is required',
        });
      }

      const reaction = await ForumMessageService.addReaction(
        messageId,
        emoji,
        userId
      );

      res.status(201).json({
        success: true,
        message: 'Reaction added successfully',
        data: reaction,
      });
    } catch (error) {
      logger.error('Error adding reaction:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to add reaction',
      });
    }
  }

  // Remove reaction from message
  static async removeReaction(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const { emoji } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!emoji) {
        res.status(400).json({
          success: false,
          message: 'Emoji is required',
        });
      }

      await ForumMessageService.removeReaction(messageId, emoji, userId);

      res.status(200).json({
        success: true,
        message: 'Reaction removed successfully',
      });
    } catch (error) {
      logger.error('Error removing reaction:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to remove reaction',
      });
    }
  }

  // Search messages in a channel
  static async searchMessages(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const { query, page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!query) {
        res.status(400).json({
          success: false,
          message: 'Search query is required',
        });
      }

      const result = await ForumMessageService.searchMessages(
        parseInt(channelId),
        query as string,
        userId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.status(200).json({
        success: true,
        data: result.messages,
        pagination: result.pagination,
        query: result.query,
      });
    } catch (error) {
      logger.error('Error searching messages:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to search messages',
      });
    }
  }

  // Get message thread (replies)
  static async getMessageThread(req: Request, res: Response) {
    try {
      const { messageId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // Get the parent message with replies
      const message = await ForumMessageService.getMessageById(
        messageId,
        userId
      );

      res.status(200).json({
        success: true,
        data: {
          parentMessage: message,
          replies: (message as any).replies || [],
        },
      });
    } catch (error) {
      logger.error('Error getting message thread:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get message thread',
      });
    }
  }
}
