import { db } from '../../utils/model';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';

export const PackageTestService = {
  getAllPackageTest: async () => {
    return await db.test.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  },

  getAllInvestigationAdmin: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_VIEW);
    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const [investigation, totalPages] = await db.$transaction([
      db.test.findMany({
        orderBy: { name: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),

      db.test.count(),
    ]);

    return {
      investigations: investigation,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalPages,
    };
  },

  createInvestigation: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_CREATE);
    const formatName = formatString.trimString(reqBody.name);

    const testExist = await db.test.findUnique({
      where: { name: formatName },
    });
    if (testExist) {
      throw new HttpError('Investigation with the name already exists', 400);
    }
    await db.test.create({
      data: {
        name: formatName,
      },
    });
    return {
      message: `${formatName} - investigation created successfully`,
    };
  },

  deleteInvestigation: async (staffId: any, id: number) => {
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_DELETE);
    const testExist = await db.test.findUnique({
      where: { id: Number(id) },
    });
    if (!testExist) {
      throw new HttpError('Investigation does exists', 400);
    }
    await db.test.delete({
      where: { id: Number(id) },
    });
    return {
      message: `${testExist?.name} - investigation deleted successfully`,
    };
  },
};
