import express, { Application } from 'express';
import cors from 'cors';
import morgan from 'morgan';
import helmet from 'helmet';
import path from 'path';
import { logger } from '../v1/utils/logger';
import { errorHandler } from '../v1/middleware/errorHandler';
import { requestId } from '../v1/middleware/requestId';
import { getRequestMetadata } from '../v1/utils/getMetadata';
import config from '../config/app.config';

/**
 * Configure and apply all middleware to the Express application
 * @param app Express application instance
 */
export const setupMiddleware = (app: Application): void => {
  // Define a custom token for morgan
  morgan.token('client-metadata', (req) => {
    const metadata = getRequestMetadata(req);
    return `StaffId: ${metadata.staffId}, IP: ${metadata.ip}, Device: ${metadata.device.device}, Device Type: ${metadata.device.deviceType}, Device Vendor: ${metadata.device.deviceVendor}, Browser: ${metadata.device.browser}, OS: ${metadata.device.os}`;
  });

  // Create a custom log format
  const format = ':method :url :status - :client-metadata';

  // Apply logging middleware
  app.use(
    morgan(format, {
      stream: {
        write: (message) => logger.info(message.trim()),
      },
    })
  );

  // Configure CORS
  const corsOptions = {
    origin: (
      origin: string | undefined,
      callback: (err: Error | null, allow?: boolean) => void
    ) => {
      if (!origin) {
        return callback(null, true);
      }
      const allowedOrigins = (config.CORS_ORIGINS || '*').split(',');
      if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'), false);
      }
    },
  };

  // Apply security and parsing middleware
  app.use(cors(corsOptions));
  app.use(helmet());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(requestId);

  // Serve static files from uploads directory
  app.use('/uploads', express.static(path.join(__dirname, '../../uploads')));

  // Set up basic routes
  app.get('/', (req, res) => {
    logger.info('Request received for homepage');
    res
      .status(200)
      .send(
        `Welcome to Cedarcrest Hospitals LTD API! - ${new Date().toISOString()}`
      );
  });
};

/**
 * Apply error handling middleware
 * @param app Express application instance
 */
export const setupErrorHandling = (app: Application): void => {
  app.use(errorHandler);
};
