import { Request, Response } from 'express';
import { FileUploadService } from '../../services/upload/fileUploadService';
import { convertMulterFiles } from '../../utils/upload/forumUpload';
import { logger } from '../../utils/logger';

export class FileUploadController {
  // Upload files for forum messages
  static async uploadForumFiles(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[];
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!files || files.length === 0) {
        res.status(400).json({
          success: false,
          message: 'No files provided',
        });
      }

      // Convert multer files to our format
      const fileData = convertMulterFiles(files);

      // Upload files
      const uploadedFiles = await FileUploadService.uploadMultipleFiles(
        fileData,
        'messages',
        { useCloudinary: true }
      );

      res.status(200).json({
        success: true,
        message: 'Files uploaded successfully',
        data: uploadedFiles,
      });
    } catch (error) {
      logger.error('Error uploading forum files:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to upload files',
      });
    }
  }

  // Upload files for game content
  static async uploadGameFiles(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[];
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!files || files.length === 0) {
        res.status(400).json({
          success: false,
          message: 'No files provided',
        });
      }

      // Convert multer files to our format
      const fileData = convertMulterFiles(files);

      // Upload files with game-specific validation
      const uploadedFiles = await FileUploadService.uploadMultipleFiles(
        fileData,
        'games',
        {
          useCloudinary: true,
          maxSize: 5 * 1024 * 1024, // 5MB for game content
          allowedTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
          ],
        }
      );

      res.status(200).json({
        success: true,
        message: 'Game files uploaded successfully',
        data: uploadedFiles,
      });
    } catch (error) {
      logger.error('Error uploading game files:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to upload game files',
      });
    }
  }

  // Upload avatar image
  static async uploadAvatar(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!file) {
        res.status(400).json({
          success: false,
          message: 'No file provided',
        });
      }

      // Convert multer file to our format
      const fileData = {
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        buffer: file.buffer,
      };

      // Upload avatar with specific validation
      const uploadedFile = await FileUploadService.uploadFile(
        fileData,
        'avatars',
        {
          useCloudinary: true,
          maxSize: 2 * 1024 * 1024, // 2MB for avatars
          allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
          publicId: `avatar_${userId}_${Date.now()}`,
        }
      );

      res.status(200).json({
        success: true,
        message: 'Avatar uploaded successfully',
        data: uploadedFile,
      });
    } catch (error) {
      logger.error('Error uploading avatar:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to upload avatar',
      });
    }
  }

  // Upload single file (generic)
  static async uploadSingleFile(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      const userId = req.Staff as unknown as number;
      const { folder = 'general' } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!file) {
        res.status(400).json({
          success: false,
          message: 'No file provided',
        });
      }

      // Convert multer file to our format
      const fileData = {
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        buffer: file.buffer,
      };

      // Upload file
      const uploadedFile = await FileUploadService.uploadFile(
        fileData,
        folder,
        { useCloudinary: true }
      );

      res.status(200).json({
        success: true,
        message: 'File uploaded successfully',
        data: uploadedFile,
      });
    } catch (error) {
      logger.error('Error uploading file:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to upload file',
      });
    }
  }

  // Delete uploaded file
  static async deleteFile(req: Request, res: Response) {
    try {
      const { fileUrl, cloudinaryPublicId } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!fileUrl) {
        res.status(400).json({
          success: false,
          message: 'File URL is required',
        });
      }

      await FileUploadService.deleteFile(fileUrl, cloudinaryPublicId);

      res.status(200).json({
        success: true,
        message: 'File deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting file:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to delete file',
      });
    }
  }

  // Get file info (for validation before upload)
  static async getFileInfo(req: Request, res: Response) {
    try {
      const { mimetype, size } = req.query;

      if (!mimetype || !size) {
        res.status(400).json({
          success: false,
          message: 'MIME type and size are required',
        });
      }

      const fileData = {
        originalName: 'temp',
        mimetype: mimetype as string,
        size: parseInt(size as string),
      };

      try {
        FileUploadService.validateFile(fileData);
        const category = FileUploadService.getFileCategory(mimetype as string);

        res.status(200).json({
          success: true,
          message: 'File is valid',
          data: {
            isValid: true,
            category,
            maxSize: 10 * 1024 * 1024, // 10MB
          },
        });
      } catch (validationError) {
        res.status(400).json({
          success: false,
          message:
            validationError instanceof Error
              ? validationError.message
              : 'File validation failed',
          data: {
            isValid: false,
          },
        });
      }
    } catch (error) {
      logger.error('Error validating file info:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate file info',
      });
    }
  }
}
