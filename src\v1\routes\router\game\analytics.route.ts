import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { GameAnalyticsController } from '../../../controllers/game/analyticsController';

export const gameAnalyticsRoute = Router();

// Get overall game statistics
gameAnalyticsRoute.get(
  '/statistics',
  secure,
  GameAnalyticsController.getGameStatistics
);

// Get participation analytics
gameAnalyticsRoute.get(
  '/participation',
  secure,
  GameAnalyticsController.getParticipationAnalytics
);

// Get reward analytics
gameAnalyticsRoute.get(
  '/rewards',
  secure,
  GameAnalyticsController.getRewardAnalytics
);

// Get game performance analytics
gameAnalyticsRoute.get(
  '/game/:gameId/performance',
  secure,
  GameAnalyticsController.getGamePerformance
);

// Get dashboard analytics (combined overview)
gameAnalyticsRoute.get(
  '/dashboard',
  secure,
  GameAnalyticsController.getDashboardAnalytics
);

// Get my game analytics (for game creators)
gameAnalyticsRoute.get(
  '/my/analytics',
  secure,
  GameAnalyticsController.getMyGameAnalytics
);

// Get location-specific analytics
gameAnalyticsRoute.get(
  '/location/:locationId',
  secure,
  GameAnalyticsController.getLocationAnalytics
);

// Get game type comparison analytics
gameAnalyticsRoute.get(
  '/comparison/types',
  secure,
  GameAnalyticsController.getGameTypeComparison
);
