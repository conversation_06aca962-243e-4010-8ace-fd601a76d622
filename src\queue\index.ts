import { Application } from 'express';
import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bull';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../v1/utils/logger';

/**
 * Setup Bull Board UI for queue monitoring
 * @param app Express application instance
 * @param queues Array of Bull queues to monitor
 */
export const setupBullBoard = (app: Application, queues: Queue[]): void => {
  // Set up Bull Board
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/admin/queues');

  // Filter out queues that aren't real Bull queues
  const validQueues = queues.filter((queue) => {
    try {
      // Check if it's a real Bull queue by accessing a property only Bull queues have
      return (
        typeof queue.name === 'string' && typeof queue.client !== 'undefined'
      );
    } catch (error) {
      logger.warn(
        `Queue ${queue?.name || 'unknown'} is not a valid Bull queue, skipping for Bull Board`
      );
      return false;
    }
  });

  if (validQueues.length === 0) {
    logger.warn('No valid Bull queues found, Bull Board will be empty');
  }

  createBullBoard({
    queues: validQueues.map((queue) => new BullAdapter(queue)),
    serverAdapter: serverAdapter,
  });

  // Use Bull Board UI
  app.use('/admin/queues', serverAdapter.getRouter());
  logger.info(
    `Bull Board UI set up at /admin/queues with ${validQueues.length} queues`
  );
};

/**
 * Dynamically load all queue processors
 */
export const loadQueueProcessors = async (): Promise<void> => {
  try {
    const processorsDir = path.join(
      __dirname,
      '../v1/jobs/queueJobs/processors'
    );

    // Check if directory exists
    try {
      await fs.access(processorsDir);
    } catch (error) {
      logger.warn(`Processors directory not found: ${processorsDir}`);
      return;
    }

    const files = await fs.readdir(processorsDir);
    const fileExtension = process.env.NODE_ENV === 'production' ? '.js' : '.ts';

    // Filter for .ts files if using TypeScript or .js files if using JavaScript
    const processorFiles = files.filter((file) => file.endsWith(fileExtension));

    await Promise.all(
      processorFiles.map(async (file) => {
        const filePath = path.join(processorsDir, file);
        try {
          await import(filePath);
          logger.info(`Loaded queue processor: ${file}`);
        } catch (error) {
          logger.error(`Error loading queue processor ${file}:`, error);
        }
      })
    );

    logger.info(`Loaded ${processorFiles.length} queue processors`);
  } catch (error) {
    logger.error('Error loading queue processors:', error);
  }
};
