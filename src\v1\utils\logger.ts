import { createLogger, format, transports } from 'winston';
import 'winston-daily-rotate-file';
import config from '../../config/app.config';

// Define log format
const logFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format.printf(({ level, message, timestamp }) => {
    return `${timestamp} [${level.toUpperCase()}]: ${message}`;
  })
);

// Create general logs transport (info and below)
const generalLogsTransport = new transports.DailyRotateFile({
  dirname: 'logs/app',
  filename: '%DATE%',
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  level: 'info',
});

// Create error logs transport (error and above)
const errorLogsTransport = new transports.DailyRotateFile({
  dirname: 'logs/error',
  filename: '%DATE%',
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  level: 'error',
});

// Create logger instance
export const logger = createLogger({
  level: config.NODE_ENV === 'production' ? 'info' : 'debug',
  format: logFormat,
  transports:
    config.NODE_ENV === 'development'
      ? [new transports.Console()] // Only console transport in development
      : [new transports.Console(), generalLogsTransport, errorLogsTransport], // All transports in production
});

/**
 * Development-only console logger
 * This function only logs to the console in development mode
 * In production, it does nothing
 */
export const devLog = (message: any, ...optionalParams: any[]): void => {
  if (config.NODE_ENV === 'development') {
    console.log(message, ...optionalParams);
  }
};
