import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import { sendToGroup, sendToUser } from '../socket';

const prisma = new PrismaClient();

export interface CreateGroupData {
  name: string;
  description?: string;
  isPrivate?: boolean;
  locationId?: number;
  createdById: number;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  isActive?: boolean;
  avatar?: string;
}

export interface InviteUserData {
  groupId: number;
  invitedById: number;
  invitedStaffId: number;
  expiresAt?: Date;
}

export class ForumGroupService {
  // Create a new forum group
  static async createGroup(data: CreateGroupData) {
    try {
      const group = await prisma.forumGroup.create({
        data: {
          name: data.name,
          description: data.description,
          isPrivate: data.isPrivate || false,
          locationId: data.locationId,
          createdById: data.createdById,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              members: true,
              channels: true,
            },
          },
        },
      });

      // Add creator as admin member
      await this.addMember({
        groupId: group.id,
        staffId: data.createdById,
        role: 'ADMIN',
      });

      // Create default general channel
      await prisma.forumChannel.create({
        data: {
          name: 'general',
          description: 'General discussion',
          groupId: group.id,
          createdById: data.createdById,
        },
      });

      logger.info(
        `Forum group created: ${group.id} by user ${data.createdById}`
      );
      return group;
    } catch (error) {
      logger.error('Error creating forum group:', error);
      throw new Error('Failed to create forum group');
    }
  }

  // Get group by ID
  static async getGroupById(groupId: number, userId: number) {
    try {
      const group = await prisma.forumGroup.findFirst({
        where: {
          id: groupId,
          isActive: true,
          OR: [
            { isPrivate: false },
            {
              isPrivate: true,
              members: {
                some: {
                  staffId: userId,
                  isActive: true,
                },
              },
            },
          ],
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          channels: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              description: true,
              isPrivate: true,
              createdAt: true,
            },
            orderBy: { createdAt: 'asc' },
          },
          members: {
            where: { isActive: true },
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
            },
            orderBy: { joinedAt: 'asc' },
          },
          _count: {
            select: {
              members: true,
              channels: true,
            },
          },
        },
      });

      if (!group) {
        throw new Error('Group not found or access denied');
      }

      return group;
    } catch (error) {
      logger.error('Error getting forum group:', error);
      throw error;
    }
  }

  // Get all groups for a user
  static async getUserGroups(userId: number, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit;

      const [groups, total] = await Promise.all([
        prisma.forumGroup.findMany({
          where: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: userId,
                    isActive: true,
                  },
                },
              },
            ],
          },
          include: {
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            location: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                members: true,
                channels: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.forumGroup.count({
          where: {
            isActive: true,
            OR: [
              { isPrivate: false },
              {
                isPrivate: true,
                members: {
                  some: {
                    staffId: userId,
                    isActive: true,
                  },
                },
              },
            ],
          },
        }),
      ]);

      return {
        groups,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting user groups:', error);
      throw new Error('Failed to get user groups');
    }
  }

  // Update group
  static async updateGroup(
    groupId: number,
    data: UpdateGroupData,
    userId: number
  ) {
    try {
      // Check if user is admin of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId,
          staffId: userId,
          role: 'ADMIN',
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins can update group settings');
      }

      const updatedGroup = await prisma.forumGroup.update({
        where: { id: groupId },
        data,
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          _count: {
            select: {
              members: true,
              channels: true,
            },
          },
        },
      });

      // Notify group members about the update
      sendToGroup(groupId, 'group_updated', {
        groupId,
        updatedBy: userId,
        changes: data,
        updatedAt: new Date(),
      });

      logger.info(`Forum group updated: ${groupId} by user ${userId}`);
      return updatedGroup;
    } catch (error) {
      logger.error('Error updating forum group:', error);
      throw error;
    }
  }

  // Add member to group
  static async addMember(data: {
    groupId: number;
    staffId: number;
    role?: 'ADMIN' | 'MODERATOR' | 'MEMBER';
  }) {
    try {
      const member = await prisma.forumGroupMember.create({
        data: {
          groupId: data.groupId,
          staffId: data.staffId,
          role: data.role || 'MEMBER',
        },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      // Notify group about new member
      sendToGroup(data.groupId, 'member_joined', {
        groupId: data.groupId,
        member,
        joinedAt: new Date(),
      });

      logger.info(`User ${data.staffId} added to group ${data.groupId}`);
      return member;
    } catch (error) {
      logger.error('Error adding group member:', error);
      throw new Error('Failed to add group member');
    }
  }

  // Remove member from group
  static async removeMember(
    groupId: number,
    staffId: number,
    removedBy: number
  ) {
    try {
      // Check if remover is admin
      const removerMembership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId,
          staffId: removedBy,
          role: 'ADMIN',
          isActive: true,
        },
      });

      if (!removerMembership && removedBy !== staffId) {
        throw new Error('Only group admins can remove members');
      }

      await prisma.forumGroupMember.update({
        where: {
          groupId_staffId: {
            groupId,
            staffId,
          },
        },
        data: { isActive: false },
      });

      // Notify group about member leaving
      sendToGroup(groupId, 'member_left', {
        groupId,
        staffId,
        removedBy,
        leftAt: new Date(),
      });

      logger.info(
        `User ${staffId} removed from group ${groupId} by ${removedBy}`
      );
      return { success: true };
    } catch (error) {
      logger.error('Error removing group member:', error);
      throw error;
    }
  }

  // Get group members
  static async getGroupMembers(groupId: number, page = 1, limit = 50) {
    try {
      const skip = (page - 1) * limit;

      const [members, total] = await Promise.all([
        prisma.forumGroupMember.findMany({
          where: {
            groupId,
            isActive: true,
          },
          include: {
            staff: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
          },
          orderBy: { joinedAt: 'asc' },
          skip,
          take: limit,
        }),
        prisma.forumGroupMember.count({
          where: {
            groupId,
            isActive: true,
          },
        }),
      ]);

      return {
        members,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting group members:', error);
      throw error;
    }
  }

  // Update member role
  static async updateMemberRole(
    groupId: number,
    staffId: number,
    role: 'ADMIN' | 'MODERATOR' | 'MEMBER',
    updatedBy: number
  ) {
    try {
      // Check if updater is admin
      const updaterMembership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId,
          staffId: updatedBy,
          role: 'ADMIN',
          isActive: true,
        },
      });

      if (!updaterMembership) {
        throw new Error('Only group admins can update member roles');
      }

      const updatedMember = await prisma.forumGroupMember.update({
        where: {
          groupId_staffId: {
            groupId,
            staffId,
          },
        },
        data: { role },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      // Notify group about role change
      sendToGroup(groupId, 'member_role_updated', {
        groupId,
        member: updatedMember,
        updatedBy,
        updatedAt: new Date(),
      });

      logger.info(
        `User ${staffId} role updated to ${role} in group ${groupId} by ${updatedBy}`
      );
      return updatedMember;
    } catch (error) {
      logger.error('Error updating member role:', error);
      throw error;
    }
  }

  // Delete group
  static async deleteGroup(groupId: number, userId: number) {
    try {
      // Check if user is admin of the group
      const membership = await prisma.forumGroupMember.findFirst({
        where: {
          groupId,
          staffId: userId,
          role: 'ADMIN',
          isActive: true,
        },
      });

      if (!membership) {
        throw new Error('Only group admins can delete the group');
      }

      // Soft delete the group
      await prisma.forumGroup.update({
        where: { id: groupId },
        data: { isActive: false },
      });

      // Notify all members about group deletion
      sendToGroup(groupId, 'group_deleted', {
        groupId,
        deletedBy: userId,
        deletedAt: new Date(),
      });

      logger.info(`Forum group deleted: ${groupId} by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting forum group:', error);
      throw error;
    }
  }

  // Search groups
  static async searchGroups(
    query: string,
    userId: number,
    page = 1,
    limit = 20
  ) {
    try {
      const skip = (page - 1) * limit;

      const [groups, total] = await Promise.all([
        prisma.forumGroup.findMany({
          where: {
            isActive: true,
            OR: [
              {
                name: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
              {
                description: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
            ],
            AND: [
              {
                OR: [
                  { isPrivate: false },
                  {
                    isPrivate: true,
                    members: {
                      some: {
                        staffId: userId,
                        isActive: true,
                      },
                    },
                  },
                ],
              },
            ],
          },
          include: {
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            location: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                members: true,
                channels: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.forumGroup.count({
          where: {
            isActive: true,
            OR: [
              {
                name: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
              {
                description: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
            ],
            AND: [
              {
                OR: [
                  { isPrivate: false },
                  {
                    isPrivate: true,
                    members: {
                      some: {
                        staffId: userId,
                        isActive: true,
                      },
                    },
                  },
                ],
              },
            ],
          },
        }),
      ]);

      return {
        groups,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error searching groups:', error);
      throw error;
    }
  }
}
