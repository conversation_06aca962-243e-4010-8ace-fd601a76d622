import fs from 'fs/promises';
import path from 'path';
import { logger, devLog } from '../../utils/logger';

const processorsDir = path.join(__dirname, 'processors');

const loadProcessors = async () => {
  try {
    const files = await fs.readdir(processorsDir);

    const fileExtension = process.env.NODE_ENV === 'production' ? '.js' : '.ts';

    // Filter for .ts files if using TypeScript or .js files if using JavaScript
    const processorFiles = files.filter((file) => file.endsWith(fileExtension));

    await Promise.all(
      processorFiles.map(async (file) => {
        const filePath = path.join(processorsDir, file);
        try {
          await import(filePath);
          devLog(`Loaded processor: ${file}`);
        } catch (error) {
          logger.error(`Error loading processor ${file}:`, error);
        }
      })
    );
  } catch (error) {
    logger.error('Error reading processors directory:', error);
  }
};

// Load all processors
loadProcessors();
