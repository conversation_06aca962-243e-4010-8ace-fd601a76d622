import { db } from '../../utils/model';
import * as bcrypt from 'bcryptjs';
import crypto from 'crypto';
import jwt, { Secret } from 'jsonwebtoken';
import config from '../../../config/app.config';
import { HttpError } from '../../utils/httpError';
import { messagingService } from '../messaging';
import { formatString } from '../../utils/stringFormatter';
// import { adminHasPermission, PERMISSIONS } from '../admin/permission';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { getInitials } from '../../utils/util';
import { createDateFilter } from '../../utils/util';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import { toTitleCase } from '../../utils/util';

export const SECRET_KEY = config.SIGNING_TOKEN_SECRET as Secret;

interface CreateStaffRequestBody {
  email: string;
  staffCode: string;
  fullName: string;
  locationId?: number;
  departmentId: number;
  role?: string;
  type?: string;
  phoneNumber: string;
  isDoctor: boolean;
  rewardIds?: (string | number)[];
  specialtyId?: string | number;
  isConsultant?: boolean;
  isVisitingConsultant?: boolean;
}

export const staffService = {
  getAllStaff: async (staffId: number, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_VIEW);
    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search: string = (query.search as string) || '';
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { fullName: { contains: search } },
              { email: { contains: search } },
              { staffCode: { contains: search } },
              { referralCode: { code: { contains: search } } },
              { location: { name: { contains: search } } },
              { department: { name: { contains: search } } },
              { unit: { name: { contains: search } } },
            ],
          }
        : {}),
      ...dateFilter,
    };

    const [staffs, totalPages, totalCount] = await db.$transaction([
      db.staff.findMany({
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        where: whereClause,
        include: {
          referralCode: {
            select: {
              id: true,
              code: true,
              isActive: true,
              _count: {
                select: {
                  referralUsages: true,
                },
              },
            },
          },
          location: {
            select: {
              name: true,
              region:{
                select:{
                  name: true
                }
              }
            },
          },
          department: {
            select: {
              name: true,
            },
          },
          unit: {
            select: {
              name: true,
            },
          },
        },
      }),
      db.staff.count({
        where: whereClause,
      }),
      db.staff.count(),
    ]);

    const discountWithPriceCount = staffs.map(({ password, ...staff }) => ({
      ...staff,
      codeUsage: staff.referralCode
        ? staff.referralCode._count.referralUsages
        : 0,
    }));

    return {
      staffs: discountWithPriceCount,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
    };
  },

  createStaff: async (staffId: number, reqBody: CreateStaffRequestBody) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
    const {
      email,
      staffCode,
      fullName,
      locationId,
      departmentId,
      type,
      phoneNumber,
      rewardIds,
      isDoctor,
      specialtyId,
      isConsultant,
      isVisitingConsultant,

      ...rest
    } = reqBody;
    const formattedString = formatString.trimString(staffCode);

    const checkStaff = await db.staff.findUnique({
      where: { staffCode: formattedString },
    });

    const another = await db.staff.findFirst({
      where: {
        OR: [
          { email: formatString.formatEmail(email) },
          { fullName: fullName.trim() },
        ],
      },
    });

    if (checkStaff || another) {
      throw new HttpError('Staff with similar details already exist', 400);
    }

    const refCode = getInitials(fullName, phoneNumber);
    const staffCreateData: any = {
      data: {
        ...rest,
        fullName: fullName.trim(),
        type: type,
        locationId: Number(locationId),
        departmentId: Number(departmentId),
        phoneNumber,
        email: formatString.formatEmail(email),
        staffCode: formattedString,
        referralCode: {
          create: {
            code: refCode,
          },
        },
      },
    };

    if (rewardIds && rewardIds.length > 0) {
      staffCreateData.data.referralCode.create.reward = {
        connect: rewardIds.map((id: string | number) => ({ id: Number(id) })),
      };
    }

    const createdStaff = await db.staff.create(staffCreateData);

    if (isDoctor) {
      const doctorProfileData: any = {
        staffId: createdStaff.id,
        isDoctor,
        isConsultant,
        isVisitingConsultant,
      };

      if (specialtyId) {
        doctorProfileData.specialtyId = Number(specialtyId);
      }

      await db.doctorProfile.create({
        data: doctorProfileData,
      });
    }

    // Send real-time notification
    await messagingService.broadcastAnnouncement(
      `New staff member ${fullName} has been added`,
      'success'
    );

    return {
      message: 'Staff created successfully',
    };
  },

  verifyStaffCode: async (reqBody: any) => {
    const refCode = formatString.trimString(reqBody.code);
    const checkStaffCode = await db.referralCode.findUnique({
      where: { code: refCode },
    });
    if (!checkStaffCode) {
      throw new HttpError('Referral code does not exist', 400);
    }

    if (!checkStaffCode.isActive) {
      throw new HttpError('Referral code has been deactivated', 400);
    }

    const packageExist = await db.package.findUnique({
      where: {
        id: Number(reqBody.id),
      },
      select: {
        name: true,
        slug: true,
        description: true,
      },
    });
    if (!packageExist) {
      throw new HttpError('Package does not exist', 400);
    }

    return {
      package: packageExist,
      message: 'Referral link generated successfully',
    };
  },

  updateStaff: async (staffId: number, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);
    // Create a new object without currentPassword
    const { currentPassword, ...dataToUpdate } = reqBody;

    const checkStaff = await db.staff.findUnique({
      where: { id: Number(reqBody.id) },
      include: {
        referralCode: true,
      },
    });
    if (!checkStaff) {
      throw new HttpError('Staff does not exist', 400);
    }

    // Handle password change if requested
    if (reqBody.password) {
      // Verify current password
      const isPasswordValid = await bcrypt.compare(
        currentPassword,
        checkStaff.password || ''
      );
      if (!isPasswordValid) {
        throw new HttpError('Enter a valid current password', 400);
      }
      // Hash the new password
      dataToUpdate.password = await bcrypt.hash(reqBody.password, 10);
    }

    await db.staff.update({
      where: { id: Number(reqBody.id) },
      data: dataToUpdate,
    });

    if (reqBody.isActive) {
      await db.referralCode.update({
        where: { id: Number(checkStaff?.referralCode?.id) },
        data: {
          isActive: reqBody.isActive,
        },
      });
    }
    return {
      message: 'Staff updated successfully',
    };
  },

  checkStaffCode: async (reqBody: any) => {
    const { phone, email, code } = reqBody;
    const formattedCode = formatString.trimString(code);

    const checkStaffExist = await db.staff.findFirst({
      where: {
        OR: [
          { email: formatString.formatEmail(email) },
          { phoneNumber: formatString.trimString(phone) },
        ],
      },
      include: {
        referralCode: true,
      },
    });
    if (!checkStaffExist) {
      throw new HttpError('Staff does not exist', 400);
    }

    if (checkStaffExist.staffCode !== formattedCode) {
      throw new HttpError('Please check the staff ID entered', 400);
    }

    if (checkStaffExist.isActive === false) {
      throw new HttpError(
        'Sorry! This account has been deactivated. Please contact the admin.',
        400
      );
    }
    return {
      name: checkStaffExist.fullName,
      code: checkStaffExist.referralCode?.code,
    };
  },

  getStaffProfile: async (staffId: number) => {
    const cacheKey = `staff:profile:${staffId}`;

    // const cachedProfile = await getCache(cacheKey);
    // if (cachedProfile) {
    //   devLog(`Admin profile for ${adminId} retrieved from cache`);
    //   return cachedProfile;
    // }

    const result = await db.staff.findUnique({
      where: { id: Number(staffId) },
      include: {
        roles: {
          include: {
            permissions: {
              select: {
                action: true,
              },
            },
          },
        },
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        department: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!result) {
      throw new HttpError('Admin account not found', 404);
    }

    const { password, locationId, departmentId, ...rest } = result;

    // await setCache(cacheKey, rest);

    return rest;
  },

  forgotPassword: async (reqBody: any) => {
    const { code } = reqBody;
    const staff = await db.staff.findUnique({
      where: { staffCode: code },
    });
    if (!staff) {
      throw new HttpError('Staff account cannot be found', 400);
    }
    if (!staff.isActive) {
      throw new HttpError('Staff account is deactivated', 400);
    }
    const name = staff.fullName.split(' ')[0];
    const password = crypto.randomBytes(4).toString('hex');
    await db.staff.update({
      where: { id: staff.id },
      data: { password: bcrypt.hashSync(password, 10) },
    });
    const mailOptions = {
      from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
      to: staff.email,
      subject: 'Your staff login password',
      template: 'staff-login',
      context: {
        name: toTitleCase(name),
        password: password,
      },
    };
    enqueueSendEmailJob(mailOptions);
    return {
      message: `A new password sent succesfully to your email address - ${staff.email}`,
    };
  },

  checkStaffId: async (reqBody: any) => {
    const { code } = reqBody;
    const staff = await db.staff.findUnique({
      where: { staffCode: code },
    });
    if (!staff) {
      throw new HttpError('Staff account cannot be found', 400);
    }
    if (!staff.isActive) {
      throw new HttpError('Staff account is deactivated', 400);
    }
    if (staff && staff.password && !staff.lastLogin) {
      return {
        message: ` Please check your email - ${staff.email} for password`,
        statusCode: 201,
      };
    }
    if (!staff.password) {
      const name = staff.fullName.split(' ')[0];
      const password = crypto.randomBytes(4).toString('hex');
      await db.staff.update({
        where: { id: staff.id },
        data: { password: bcrypt.hashSync(password, 10) },
      });
      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: staff.email,
        subject: 'Your staff login password',
        template: 'staff-login',
        context: {
          name: toTitleCase(name),
          password: password,
        },
      };
      enqueueSendEmailJob(mailOptions);
      return {
        message: `Password sent succesfully to your email address - ${staff.email}`,
        statusCode: 201,
      };
    }
    return { message: 'Staff ID verified' };
  },

  loginStaff: async (reqBody: any) => {
    const { code, password } = reqBody;
    const staff = await db.staff.findUnique({
      where: { staffCode: code },
    });
    if (!staff || !staff.isActive) {
      throw new HttpError('Invalid credentials', 400);
    }
    const isPasswordValid = await bcrypt.compare(
      password,
      staff.password || ''
    );
    if (!isPasswordValid) {
      throw new HttpError('Enter a valid password', 400);
    }
    const token = jwt.sign({ id: staff.id }, SECRET_KEY, {
      expiresIn: '7 days',
    });
    await db.staff.update({
      where: { id: staff.id },
      data: { lastLogin: new Date() },
    });

    return {
      sub: staff.id,
      token: token,
    };
  },

  listRole: async (staffId: any) => {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_VIEW);
    return db.role.findMany({
      include: {
        permissions: true,
      },
    });
  },
  getConsultants: async () => {
    const result = await db.staff.findMany({
      where: {
        isActive: true,
        doctorProfile: {
          isDoctor: true,
          isConsultant: true,
        },
      },
      select: {
        id: true,
        fullName: true,
      },
    });

    return result.map(({ fullName, ...rest }) => ({
      ...rest,
      name: fullName,
    }));
  },
};
