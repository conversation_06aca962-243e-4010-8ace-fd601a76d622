import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { FileUploadController } from '../../../controllers/upload/fileUploadController';
import {
  forumUpload,
  gameUpload,
  avatarUpload,
  handleMulterError,
} from '../../../utils/upload/forumUpload';

export const fileUploadRoute = Router();

// Upload multiple files for forum messages
fileUploadRoute.post(
  '/forum/files',
  secure,
  forumUpload.array('files', 5), // Maximum 5 files
  handleMulterError,
  FileUploadController.uploadForumFiles
);

// Upload files for game content
fileUploadRoute.post(
  '/game/files',
  secure,
  gameUpload.array('files', 3), // Maximum 3 files
  handleMulterError,
  FileUploadController.uploadGameFiles
);

// Upload avatar image
fileUploadRoute.post(
  '/avatar',
  secure,
  avatarUpload.single('avatar'),
  handleMulterError,
  FileUploadController.uploadAvatar
);

// Upload single file (generic)
fileUploadRoute.post(
  '/single',
  secure,
  forumUpload.single('file'),
  handleMulterError,
  FileUploadController.uploadSingleFile
);

// Delete uploaded file
fileUploadRoute.delete('/delete', secure, FileUploadController.deleteFile);

// Validate file info before upload
fileUploadRoute.get('/validate', secure, FileUploadController.getFileInfo);
