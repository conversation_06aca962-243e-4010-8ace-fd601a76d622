import cron from 'node-cron';
import { logger } from '../../utils/logger';
import { deactivateExpiredPriceModifiers } from '../../services/discount/priceModifier';

/**
 * Cron job to automatically set isActive to false for price modifiers whose endDate has passed
 * Runs daily at midnight (00:00)
 * @returns The scheduled task that can be used to stop the job
 */
export const setupPriceModifierExpirationJob = (): cron.ScheduledTask => {
  // Schedule the job to run at midnight every day
  // Cron format: second(optional) minute hour day-of-month month day-of-week
  return cron.schedule(
    '0 0 * * *',
    async () => {
      try {
        logger.info('Running price modifier expiration check job');

        // Use the shared function to deactivate expired price modifiers
        const deactivatedCount = await deactivateExpiredPriceModifiers();

        if (deactivatedCount === 0) {
          logger.info('No expired price modifiers found');
        } else {
          logger.info(
            `Deactivated ${deactivatedCount} expired price modifiers`
          );
        }
      } catch (error) {
        logger.error('Error in price modifier expiration job:', error);
      }
    },
    {
      scheduled: true,
      timezone: 'UTC', // You can adjust this to your local timezone if needed
    }
  );

  logger.info('Price modifier expiration job scheduled');
};
