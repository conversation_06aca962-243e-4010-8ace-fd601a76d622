import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';

export const locationService = {
  getAllLocations: async () => {
    return await db.location.findMany({
      include: {
        region: true,
      }
    });
  },

  createLocation: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.LOCATION_CREATE);

    const checkLocation = await db.location.findFirst({
      where: { name: reqBody.name },
    });
    if (checkLocation) {
      throw new HttpError('Location already exists', 400);
    }
    await db.location.create({
      data: {
        ...reqBody,
      },
    });
    return {
      message: `${reqBody.name} - location created successfully`,
    };
  },

  updateLocation: async (staffId: any, reqBody: any) => {
    const updateData = { ...reqBody };
    await staffHasPermission(staffId, PERMISSIONS.LOCATION_EDIT);

    const checkLocation = await db.location.findUnique({
      where: { id: Number(reqBody.id) },
    });
    if (checkLocation && checkLocation.name !== reqBody.name) {
      return db.location.update({
        where: { id: reqBody.id },
        data: updateData,
      });
    } else {
      throw new HttpError('Edit location not allowed', 404);
    }
  },

  deleteLocation: async (staffId: any, locationId: number) => {
    await staffHasPermission(staffId, PERMISSIONS.LOCATION_DELETE);

    const checkLocation = await db.location.findUnique({
      where: { id: Number(locationId) },
    });
    if (!checkLocation) {
      throw new HttpError('Location does not exist', 404);
    }
    await db.location.delete({
      where: { id: Number(locationId) },
    });
    return { message: 'Location deleted successfully' };
  },
};
