<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Delete Package Price Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #333;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input,
      select {
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #45a049;
      }
      #result {
        margin-top: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
        min-height: 100px;
      }
      .error {
        color: red;
      }
      .success {
        color: green;
      }
    </style>
  </head>
  <body>
    <h1>Delete Package Price Test</h1>

    <div class="form-group">
      <label for="packageId">Package ID:</label>
      <input type="number" id="packageId" name="packageId" required />
    </div>

    <div class="form-group">
      <label for="priceId">Price ID:</label>
      <input type="number" id="priceId" name="priceId" required />
    </div>

    <button id="deleteBtn">Delete Price</button>

    <div id="result"></div>

    <script>
      document
        .getElementById('deleteBtn')
        .addEventListener('click', async function () {
          const packageId = document.getElementById('packageId').value;
          const priceId = document.getElementById('priceId').value;
          const resultDiv = document.getElementById('result');

          if (!packageId || !priceId) {
            resultDiv.innerHTML =
              '<p class="error">Please enter both Package ID and Price ID</p>';
            return;
          }

          try {
            resultDiv.innerHTML = '<p>Sending request...</p>';

            const response = await fetch(
              `/api/v1/package/delete-package-price/${packageId}/${priceId}`,
              {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            );

            const data = await response.json();

            if (response.ok) {
              resultDiv.innerHTML = `
                        <p class="success">Success: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            } else {
              resultDiv.innerHTML = `
                        <p class="error">Error: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            }
          } catch (error) {
            resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
          }
        });
    </script>
  </body>
</html>
