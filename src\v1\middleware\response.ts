import { Request, Response, NextFunction } from 'express';

interface ResponseData {
  success: boolean;
  message: string;
  data?: any;
}

const createResponse = (
  success: boolean,
  message: string,
  data: any = null
): ResponseData => ({
  success,
  message,
  data,
});

export const successResponse = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.success = (message: string, data: any = null) => {
    res.status(200).json(createResponse(true, message, data));
  };
  next();
};

export const errorResponse = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.error = (message: string, data: any = null, statusCode: number = 400) => {
    res.status(statusCode).json(createResponse(false, message, data));
  };
  next();
};

export const globalErrorResponse = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.global = () => {
    res.status(500).json('Internal Server Error');
  };
  next();
};
