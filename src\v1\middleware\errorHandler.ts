import type { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '../utils/logger';
import { AppError } from '../utils/error';

interface ErrorResponse {
  status: string;
  message: string;
  code?: string;
  data?: any;
  stack?: string;
}

export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = 500;
  const response: ErrorResponse = {
    status: 'error',
    message: 'Internal server error',
  };

  // Handle custom AppError instances
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    response.status = statusCode < 500 ? 'fail' : 'error';
    response.message = err.message;
    response.code = err.code;
    response.data = err.data;
  }

  // Handle Prisma errors
  else if (err instanceof Prisma.PrismaClientKnownRequestError) {
    switch (err.code) {
      case 'P2002': // Unique constraint violation
        statusCode = 409;
        response.status = 'fail';
        response.message = 'A record with this data already exists';
        response.code = 'UNIQUE_CONSTRAINT_VIOLATION';
        break;
      case 'P2025': // Record not found
        statusCode = 404;
        response.status = 'fail';
        response.message = 'Record not found';
        response.code = 'RECORD_NOT_FOUND';
        break;
      case 'P2003': // Foreign key constraint violation
        statusCode = 400;
        response.status = 'fail';
        response.message = 'Invalid relationship reference';
        response.code = 'FOREIGN_KEY_VIOLATION';
        break;
      default:
        statusCode = 500;
        response.message = 'Database error occurred';
        response.code = 'DATABASE_ERROR';
    }
    response.data = {
      code: err.code,
      meta: err.meta,
    };
  }

  // Handle validation errors (e.g., from express-validator)
  else if (err.name === 'ValidationError') {
    statusCode = 400;
    response.status = 'fail';
    response.message = 'Validation failed';
    response.code = 'VALIDATION_ERROR';
  }

  // Handle JWT errors
  else if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    response.status = 'fail';
    response.message = 'Invalid token';
    response.code = 'INVALID_TOKEN';
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    response.status = 'fail';
    response.message = 'Token expired';
    response.code = 'TOKEN_EXPIRED';
  }

  // Add stack trace in development environment
  // if (process.env.NODE_ENV === 'development') {
  //   response.stack = err.stack;
  // }

  // Log the error
  logger.error('Error occurred', {
    statusCode,
    ...response,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userId: (req as any).user?.id,
    staffId: (req as any).staff?.id,
    requestId: (req as any).requestId,
    body: req.body,
    query: req.query,
    params: req.params,
    stack: err.stack,
  });

  res.status(statusCode).json(response);
};
