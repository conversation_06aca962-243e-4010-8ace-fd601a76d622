import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';

export const rewardService = {
  getAllReward: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);
    const deactivated = query.status as string | undefined;

    const status =
      deactivated === 'true'
        ? true
        : deactivated === 'false'
          ? false
          : undefined;
    return db.reward.findMany({
      where: {
        deactivated: status,
      },
    });
  },

  updateReward: async (staffId: any, reqBody: any) => {
    const updateData = { ...reqBody };
    await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

    const checkReward = await db.reward.findFirst({
      where: { id: Number(reqBody.id) },
    });
    if (!checkReward) {
      throw new HttpError('Reward type does not exist', 400);
    }
    await db.reward.update({
      where: { id: Number(reqBody.id) },
      data: updateData,
    });
    return { message: 'Reward updated successfully' };
  },

  createReward: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);
    const checkReward = await db.reward.findFirst({
      where: {
        name: reqBody.name,
        deactivated: false,
      },
    });
    if (checkReward) {
      throw new HttpError(
        'Active reward type with this name already exists',
        400
      );
    }
    return db.reward.create({
      data: reqBody,
    });
  },

  deleteReward: async (staffId: any, rewardId: number) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_DELETE);

    const checkReward = await db.reward.findUnique({
      where: { id: Number(rewardId) },
    });
    if (!checkReward) {
      throw new HttpError('Reward type does not exist', 400);
    }
    await db.reward.delete({
      where: { id: Number(rewardId) },
    });
  },

  verifyReferralCode: async (reqBody: any) => {
    const refCode = formatString.formatUpperCase(reqBody.code);
    const checkStaffCode = await db.referralCode.findUnique({
      where: { code: refCode },
    });
    if (!checkStaffCode) {
      throw new HttpError('Referral code does not exist', 400);
    }

    if (!checkStaffCode.isActive) {
      throw new HttpError('Referral code has been deactivated', 400);
    }

    return { message: 'Code still active' };
  },
};
