import Redis from 'ioredis';
import config from '../../config/app.config';
import { logger, devLog } from './logger';

const REDIS_URL = config.REDIS_DATABASE_URL || '';

// Create a Redis client singleton
class RedisClientSingleton {
  private static instance: Redis | null = null;

  public static getInstance(): Redis {
    if (!RedisClientSingleton.instance) {
      // Configure Redis options for Bull compatibility
      const redisOptions = {
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        retryStrategy: (times: number) => {
          if (times > 10) {
            logger.error('Redis connection retried too many times, giving up');
            return null; // Stop retrying
          }
          const delay = Math.min(times * 100, 3000); // Exponential backoff with max 3s
          devLog(`Redis connection retry in ${delay}ms`);
          return delay;
        },
      };

      const redis = new Redis(REDIS_URL, redisOptions);

      redis.on('connect', () => {
        logger.info('Connected to Redis');
      });

      redis.on('error', (err) => {
        logger.error('Error connecting to Redis:', err);
      });

      redis.on('reconnecting', () => {
        logger.info('Reconnecting to Redis');
      });

      redis.on('end', () => {
        logger.info('Redis connection closed');
      });

      RedisClientSingleton.instance = redis;
    }

    return RedisClientSingleton.instance;
  }
}

// Export the Redis client instance
export const redis = RedisClientSingleton.getInstance();

// Default cache TTL (1 hour in seconds)
const DEFAULT_TTL = 3600;

/**
 * Set a value in the Redis cache
 * @param key - The cache key
 * @param value - The value to cache (will be JSON stringified)
 * @param ttl - Time to live in seconds (optional, defaults to 1 hour)
 */
export const setCache = async (
  key: string,
  value: any,
  ttl: number = DEFAULT_TTL
): Promise<void> => {
  try {
    const stringValue = JSON.stringify(value);
    await redis.set(key, stringValue, 'EX', ttl);
  } catch (error) {
    logger.error(`Error setting cache for key ${key}:`, error);
  }
};

/**
 * Get a value from the Redis cache
 * @param key - The cache key
 * @returns The cached value (parsed from JSON) or null if not found
 */
export const getCache = async <T>(key: string): Promise<T | null> => {
  try {
    const value = await redis.get(key);
    if (!value) return null;
    return JSON.parse(value) as T;
  } catch (error) {
    logger.error(`Error getting cache for key ${key}:`, error);
    return null;
  }
};

/**
 * Delete a value from the Redis cache
 * @param key - The cache key to delete
 */
export const deleteCache = async (key: string): Promise<void> => {
  try {
    await redis.del(key);
  } catch (error) {
    logger.error(`Error deleting cache for key ${key}:`, error);
  }
};

/**
 * Delete multiple values from the Redis cache using a pattern
 * @param pattern - The pattern to match keys (e.g., "admin:*")
 */
export const deleteCacheByPattern = async (pattern: string): Promise<void> => {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      devLog(`Deleted ${keys.length} keys matching pattern: ${pattern}`);
    } else {
      devLog(`No keys found matching pattern: ${pattern}`);
    }
  } catch (error) {
    logger.error(`Error deleting cache by pattern ${pattern}:`, error);
  }
};
