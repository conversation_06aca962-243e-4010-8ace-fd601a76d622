import { Router } from 'express';
import { menuController } from '../../controllers/cafeteria/menu.controller';
import { inventoryController } from '../../controllers/cafeteria/inventory.controller';
import { ordersController } from '../../controllers/cafeteria/orders.controller';
import { secure } from '../../middleware/auth';
import { uploads } from '../../utils/image/uploadImage';

const router = Router();

// Menu routes
router.get('/menu-category/list', secure, menuController.getAllMenuCategory);
router.get('/menu/list', secure, menuController.getAllMenuItems);
router.post('/menu/add-new', secure, menuController.createMenuItem);
router.patch('/menu/update', secure, menuController.updateMenuItem);

// Inventory routes
router.post(
  '/inventory/add-new',
  secure,
  inventoryController.createInventoryItem
);
router.post(
  '/inventory/add-category',
  secure,
  inventoryController.createInventorycat
);
router.post(
  '/inventory/issue-stock',
  secure,
  inventoryController.issueStockItem
);
router.post(
  '/inventory/new-supply',
  secure,
  uploads.single('invoiceImage'),
  inventoryController.addInventorySupply
);

router.get(
  '/inventory/list-category',
  secure,
  inventoryController.getAllInventoryCat
);
router.get('/inventory/list', secure, inventoryController.getAllInventoryItems);
router.get(
  '/inventory/issued-stock',
  secure,
  inventoryController.getIssuedStock
);

router.get(
  '/inventory/:inventoryId/supplies',
  secure,
  inventoryController.getSupplyHistory
);

// Orders routes
router.post('/orders/check-payment', secure, ordersController.staffPayment);
router.post('/orders/new', secure, ordersController.createOrder);
router.get('/orders/list', secure, ordersController.getAllOrders);
router.get('/orders/statistics', secure, ordersController.getOrderStats);
router.get('/orders/single-order/:id', secure, ordersController.getOrderById);

export default router;
