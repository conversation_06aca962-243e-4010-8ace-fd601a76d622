import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { transactionService } from '../../services/transaction';

const ListAllTransactions = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.getAllTransactions,
    req.query,
    res,
    staffId
  );
};

export const transactionControllers = {
  ListAllTransactions,
};
