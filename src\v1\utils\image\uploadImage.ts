const multer = require('multer'); // eslint-disable-line

const storage = multer.diskStorage({});

// const fileFilter = (req: any, file: any, cb: any) => {
//   if (file.mimetype.startsWith('image')) {
//     cb(null, true);
//   } else {
//     cb('invalid image file!', false);
//   }
// };
// export const uploads = multer({
//   storage,
//   fileFilter,
// });
const fileName = (req: any, file: any, cb: any) => {
  cb(null, Date.now() + '-' + file.originalname);
};

export const uploads = multer({
  storage,
  fileName,
  // fileFilter,
});
