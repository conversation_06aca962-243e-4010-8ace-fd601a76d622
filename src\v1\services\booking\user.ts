import { db } from '../../utils/model';
import { formatString } from '../../utils/stringFormatter';
import { HttpError } from '../../utils/httpError';

export const userService = {
  checkUser: async (accountId: any, query: any) => {
    const email = query.email;
    const formattedEmail = formatString.formatEmail(email);
    let userExist = await db.user.findUnique({
      where: { emailAddress: formattedEmail },
    });
    if (!userExist) {
      throw new HttpError('Patient not found', 400);
    }
    return userExist;
  },

  createOrUpdateUser: async (reqBody: any) => {
    const {
      emailAddress,
      firstName,
      lastName,
      dateOfBirth,
      phoneNumber,
      gender,
    } = reqBody;
    const formattedEmail = formatString.formatEmail(emailAddress);
    let userExist = await db.user.findFirst({
      where: { emailAddress: formattedEmail },
    });
    if (!userExist) {
      userExist = await db.user.create({
        data: {
          emailAddress: formattedEmail,
          firstName,
          lastName,
          dateOfBirth,
          phoneNumber,
          gender,
        },
      });
    } else if (
      !userExist.firstName ||
      !userExist.lastName ||
      !userExist.dateOfBirth ||
      !userExist.phoneNumber ||
      !userExist.gender
    ) {
      userExist = await db.user.update({
        where: { id: userExist.id },
        data: {
          firstName: firstName || userExist.firstName,
          lastName: lastName || userExist.lastName,
          dateOfBirth: dateOfBirth || userExist.dateOfBirth,
          phoneNumber: phoneNumber || userExist.phoneNumber,
          gender: gender || userExist.gender,
        },
      });
    }
    return userExist;
  },

  checkAndCreateUser: async (reqBody: any) => {
    const { uhid, phone, emailAddress, ...rest } = reqBody;
    const formattedEmail = formatString.formatEmail(emailAddress);
    let userExist = await db.user.findFirst({
      where: {
        OR: [
          { uhid: uhid },
          { phoneNumber: phone },
          { emailAddress: formattedEmail },
        ],
      },
    });
    if (!userExist) {
      userExist = await db.user.create({
        data: {
          emailAddress: formattedEmail,
          uhid: uhid,
          phoneNumber: phone,
          ...rest,
        },
      });
    }
    return userExist;
  },

  checkAndRewardReferral: async (code: any) => {
    const referralCode = await db.referralCode.findUnique({
      where: { code: code },
    });
    if (!referralCode) {
      return;
    }
    return referralCode;
  },

  // updateReward: async (adminId: string, reqBody: any) => {
  //   const updateData = { ...reqBody };
  //   await adminCheckService.checkAdminAuthorization(adminId);

  //   const checkReward = await db.reward.findUnique({
  //     where: { id: reqBody.id },
  //   });
  //   if (!checkReward) {
  //     throw new HttpError('Reward type does not exist', 400);
  //   }
  //   return db.reward.update({
  //     where: { id: reqBody.id },
  //     data: updateData,
  //   });
  // },
};
