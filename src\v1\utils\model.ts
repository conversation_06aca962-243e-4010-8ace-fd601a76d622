import { PrismaClient } from '@prisma/client';
import { logger, devLog } from './logger';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient;
};

export const db = globalForPrisma.prisma || new PrismaClient();

// new PrismaClient({
//   transactionOptions: {
//     isolationLevel: 'ReadCommitted',
//     timeout: 5000, // 1 sec
//     maxWait: 10000  // 2 sec
//   }
// })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db;

export async function connectDB() {
  try {
    await db.$connect();
    logger.info('Database connected successfully');
  } catch (error) {
    logger.error('Database connection error:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}
