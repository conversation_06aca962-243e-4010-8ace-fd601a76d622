import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { formatString } from '../../utils/stringFormatter';
import * as fs from 'fs';
import * as path from 'path';

export const inventoryService = {
  getAllInventoryItems: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search: string = (query.search as string) || '';

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { itemName: { contains: search, mode: 'insensitive' } },
              {
                inventoryCat: {
                  name: { contains: search, mode: 'insensitive' },
                },
              },
            ],
          }
        : {}),
      ...(locationId !== undefined ? { locationId } : {}),
    };

    const [inventory, totalCount, analytics] = await db.$transaction([
      db.cafeteriaInventory.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          supplies: true,
          inventoryCat: true,
        },
      }),
      db.cafeteriaInventory.count({
        where: whereClause,
      }),
      db.cafeteriaInventory.findMany(),
    ]);

    const { totalStock, totalLowStock, totalOutOfStock } = analytics.reduce(
      (acc, { currentStock, minimumStock }) => {
        acc.totalStock += 1;

        if (currentStock.equals(0)) {
          acc.totalOutOfStock += 1;
        }

        if (currentStock.lessThan(minimumStock)) {
          acc.totalLowStock += 1;
        }

        return acc;
      },
      { totalStock: 0, totalLowStock: 0, totalOutOfStock: 0 }
    );

    const response = {
      inventory: inventory,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      totalStock: totalStock,
      totalLowStock: totalLowStock,
      totalOutOfStock: totalOutOfStock,
      currentPage: page,
      limit: limit,
    };

    return response;
  },

  getInventoryCategory: async (staffId: any) => {
    await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_INVENTORY_MANAGE);

    return db.cafeteriaInventoryCat.findMany();
  },

  createInventoryCategory: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const isSuperUser = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);

    if (!isSuperUser) {
      throw new HttpError('You do not have access', 403);
    }

    const formattedName = formatString.trimString(reqBody.name);

    const checkCategory = await db.cafeteriaInventoryCat.findFirst({
      where: { name: formattedName },
    });
    if (checkCategory) {
      throw new HttpError('Category with the name already exists', 400);
    }
    await db.cafeteriaInventoryCat.create({
      data: {
        name: formattedName,
        createdBy: reqBody.profile,
      },
    });
    return {
      message: `${reqBody.name} - Inventory category created successfully`,
    };
  },

  createInventoryItem: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }
    const locationId = await auth.getLocationId();
    const { itemName, category, ...rest } = reqBody;
    const formattedName = formatString.trimString(itemName);
    const existingItem = await db.cafeteriaInventory.findFirst({
      where: { itemName: formattedName, locationId: Number(locationId) },
    });

    if (existingItem) {
      throw new HttpError('Inventory already exists', 400);
    }
    await db.cafeteriaInventory.create({
      data: {
        inventoryCategoryId: Number(category),
        itemName: formattedName,
        locationId: Number(locationId),
        ...rest,
      },
    });
    return { message: `Inventory item created successfully` };
  },

  issueInventory: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }
    const { inventoryItemId, quantity, ...rest } = reqBody;

    return db.$transaction(async (tx) => {
      const inventoryItem = await tx.cafeteriaInventory.findUnique({
        where: { id: Number(inventoryItemId) },
        select: { currentStock: true },
      });

      if (!inventoryItem) {
        throw new Error('Inventory item not found.');
      }

      const currentStock = Number(inventoryItem.currentStock); // Prisma Decimal to JS number
      const issueQty = Number(quantity);

      if (issueQty > currentStock) {
        throw new HttpError(
          'Insufficient stock to issue the requested quantity.',
          400
        );
      }

      await tx.cafeteriaStockIssue.create({
        data: {
          inventoryItemId: Number(inventoryItemId),
          quantity: issueQty,
          ...rest,
        },
      });

      await tx.cafeteriaInventory.update({
        where: { id: Number(inventoryItemId) },
        data: {
          currentStock: {
            decrement: issueQty,
          },
        },
      });

      return { message: 'Stock issued successfully' };
    });
  },

  addSupply: async (staffId: any, filePath: any, reqBody: any) => {
    const {
      supplier,
      quantitySupplied,
      costPerUnit,
      inventoryItemId,
      originalName,
      ...rest
    } = reqBody;
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const locationId = await auth.getLocationId();

    let invoicePath = null;
    if (filePath) {
      const invoicesDir = path.join(process.cwd(), 'uploads', 'invoices');
      if (!fs.existsSync(invoicesDir)) {
        fs.mkdirSync(invoicesDir, { recursive: true });
      }

      const supplierName = formatString.formatSlug(supplier);
      const ext = path.extname(originalName);
      const fileName = `invoice_${supplierName}_${Date.now()}${ext}`;
      const fullPath = path.join(invoicesDir, fileName);
      fs.copyFileSync(filePath, fullPath);
      invoicePath = `uploads/invoices/${fileName}`;
    }

    return db.$transaction(async (tx) => {
      const supply = await tx.cafeteriaSupply.create({
        data: {
          inventoryItemId: Number(inventoryItemId),
          locationId: Number(locationId),
          quantitySupplied: Number(quantitySupplied),
          costPerUnit: Number(costPerUnit),
          totalCost: Number(quantitySupplied) * Number(costPerUnit),
          supplier,
          ...rest,
          ...(invoicePath && { invoice: invoicePath }),
        },
      });

      await tx.cafeteriaInventory.update({
        where: { id: Number(inventoryItemId) },
        data: {
          currentStock: {
            increment: Number(quantitySupplied),
          },
        },
      });

      return { message: 'Supply added successfully' };
    });
  },

  getSupplyHistory: async (staffId: any, inventoryId: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    // Optional permission check
    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);

    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    return db.cafeteriaSupply.findMany({
      where: {
        inventoryItemId: Number(inventoryId),
        ...(locationId !== undefined ? { locationId } : {}),
      },
      orderBy: { createdAt: 'desc' },
    });
  },
  getStockIssued: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);

    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search: string = (query.search as string) || '';
    const issueId: number = parseInt(query.issueId as string);

    const [stock, totalCount] = await db.$transaction([
      db.cafeteriaStockIssue.findMany({
        where: {
          inventoryItemId: Number(issueId),
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.cafeteriaStockIssue.count({
        where: { inventoryItemId: Number(issueId) },
      }),
    ]);

    const newStock = stock.map((item) => {
      if (hasLocationAll) {
        return { ...item };
      } else {
        const { issuedBy, ...rest } = item;
        return rest;
      }
    });

    const response = {
      stock: stock,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },
};
