import { Response } from 'express';
import { HttpError } from '../../utils/httpError';
import { logger, devLog } from '../../utils/logger';

type OperationHandler<T> = (
  userId: any,
  filePath: any,
  reqBody: any
) => Promise<T>;

//operations
export const withImageControllerOperations = async <T>(
  handler: OperationHandler<T>,
  res: Response,
  filePath?: string,
  userId?: string | number,
  reqBody?: any
) => {
  try {
    const result = await handler(userId, filePath, reqBody);
    return res.success('Successful', result);
  } catch (error) {
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        message: error.message,
        data: null,
      });
    } else if (error instanceof Error) {
      // Error is a general Error instance
      return res.status(400).json({ message: error.message, data: null });
    } else {
      // Fallback for unknown error types
      logger.error('An unknown error occurred');
    }
    logger.error('Error details:', error);
    return res.global;
  }
};
