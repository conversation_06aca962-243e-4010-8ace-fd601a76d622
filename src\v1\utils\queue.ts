import Bull, { Queue, QueueOptions } from 'bull';
import { redis } from './cache';
import { RedisOptions } from 'ioredis';
import { logger } from './logger';

// Define queue types
export interface QueueTypes {
  'send-email': {
    mailOptions: any;
  };
}

// Create a map to store all queues
const queues: Map<string, Queue> = new Map();

// Default Bull queue options with improved settings
const defaultQueueOptions: QueueOptions = {
  createClient: (
    type: 'client' | 'subscriber' | 'bclient',
    _redisOpts?: RedisOptions
  ) => {
    switch (type) {
      case 'client':
        return redis;
      case 'subscriber':
        return redis.duplicate();
      case 'bclient':
        return redis.duplicate();
      default:
        return redis;
    }
  },
  defaultJobOptions: {
    attempts: 3, // Reduced from 5 to 3
    backoff: {
      type: 'exponential', // Changed from fixed to exponential
      delay: 5000, // Start with 5 seconds, then exponential backoff
    },
    removeOnComplete: true,
    removeOnFail: true, // Changed to true to clean up failed jobs
    timeout: 30000, // 30 second timeout for jobs
  },
  settings: {
    stalledInterval: 30000, // Check for stalled jobs every 30 seconds
    maxStalledCount: 2, // Consider a job stalled after 2 checks
    lockDuration: 30000, // Lock duration of 30 seconds
    lockRenewTime: 15000, // Renew locks every 15 seconds
    retryProcessDelay: 5000, // Wait 5 seconds before retrying process if it crashes
  },
};

/**
 * Get or create a Bull queue with improved error handling
 * @param name Queue name
 * @param options Optional queue options
 * @returns Bull queue instance
 */
export function getQueue<T extends keyof QueueTypes>(
  name: T,
  options: QueueOptions = {}
): Queue<QueueTypes[T]> {
  if (!queues.has(name as string)) {
    try {
      const queueOptions = { ...defaultQueueOptions, ...options };
      const queue = new Bull(name as string, queueOptions);

      // Set up comprehensive error handling
      queue.on('error', (error) => {
        logger.error(`Queue ${name} error:`, error);
      });

      queue.on('failed', (job, error) => {
        logger.error(`Job ${job.id} in queue ${name} failed:`, error);
      });

      queue.on('stalled', (jobId) => {
        logger.warn(`Job ${jobId} in queue ${name} stalled`);
      });

      queue.on('completed', (job) => {
        logger.info(`Job ${job.id} in queue ${name} completed successfully`);
      });

      queue.on('waiting', (jobId) => {
        logger.info(`Job ${jobId} in queue ${name} is waiting`);
      });

      queue.on('active', (job) => {
        logger.info(`Job ${job.id} in queue ${name} is active`);
      });

      queue.on('cleaned', (jobs, type) => {
        logger.info(`Cleaned ${jobs.length} ${type} jobs from queue ${name}`);
      });

      // Set up periodic cleaning of completed and failed jobs
      setInterval(async () => {
        try {
          // Clean jobs older than 1 hour
          const cleaned = await queue.clean(3600000, 'completed');
          if (cleaned.length > 0) {
            logger.info(
              `Cleaned ${cleaned.length} completed jobs from queue ${name}`
            );
          }

          const cleanedFailed = await queue.clean(3600000, 'failed');
          if (cleanedFailed.length > 0) {
            logger.info(
              `Cleaned ${cleanedFailed.length} failed jobs from queue ${name}`
            );
          }
        } catch (error) {
          logger.error(`Error cleaning queue ${name}:`, error);
        }
      }, 3600000); // Run every hour

      queues.set(name as string, queue);
      logger.info(`Queue ${name} initialized successfully`);
    } catch (error) {
      logger.error(`Failed to initialize queue ${name}:`, error);
      throw error; // Re-throw to allow caller to handle
    }
  }

  return queues.get(name as string) as Queue<QueueTypes[T]>;
}

/**
 * Gracefully shut down all queues
 */
export async function closeQueues(): Promise<void> {
  try {
    const closePromises = Array.from(queues.values()).map(async (queue) => {
      try {
        await queue.close();
        logger.info(`Queue ${queue.name} closed successfully`);
      } catch (error) {
        logger.error(`Error closing queue ${queue.name}:`, error);
      }
    });

    await Promise.all(closePromises);
    logger.info('All Bull queues closed successfully');
  } catch (error) {
    logger.error('Error during queue shutdown:', error);
  }
}

// For backward compatibility with existing code - with error handling
let emailQueue: Queue<QueueTypes['send-email']>;
try {
  emailQueue = getQueue('send-email');
  logger.info('Email queue initialized successfully');
} catch (error) {
  logger.error(
    'Failed to initialize email queue, creating a dummy queue:',
    error
  );
  // Create a dummy queue that logs operations but doesn't actually do anything
  emailQueue = {
    add: async () => {
      logger.warn('Email queue is not available, email will not be sent');
      return { id: 'dummy-job-id' } as any;
    },
    process: () => {
      logger.warn(
        'Email queue is not available, processor will not be registered'
      );
      return {} as any;
    },
    on: () => {
      return {} as any;
    },
    // Add other required methods as needed
  } as any;
}

export { emailQueue };
