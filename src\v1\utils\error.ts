export class AppError extends <PERSON>rror {
  constructor(
    public statusCode: number,
    public message: string,
    public code?: string,
    public data?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export class NotFoundError extends AppError {
  constructor(message = 'Resource not found', data?: any) {
    super(404, message, 'NOT_FOUND', data);
  }
}

export class ValidationError extends AppError {
  constructor(message = 'Validation failed', data?: any) {
    super(400, message, 'VALIDATION_ERROR', data);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized access', data?: any) {
    super(401, message, 'UNAUTHORIZED', data);
  }
}

export class ForbiddenError extends AppError {
  constructor(message = 'Access forbidden', data?: any) {
    super(403, message, 'FORBIDDEN', data);
  }
}

export class ConflictError extends AppError {
  constructor(message = 'Resource conflict', data?: any) {
    super(409, message, 'CONFLICT', data);
  }
}

export class DatabaseError extends AppError {
  constructor(message = 'Database error occurred', data?: any) {
    super(500, message, 'DATABASE_ERROR', data);
  }
}
