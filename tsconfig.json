{"compileOnSave": false, "compilerOptions": {"target": "es2017", "lib": ["es2017", "esnext.asynciterable"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "pretty": true, "declaration": true, "resolveJsonModule": true, "baseUrl": "src", "module": "commonjs", "allowJs": true, "checkJs": true, "outDir": "dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.spec.ts"]}