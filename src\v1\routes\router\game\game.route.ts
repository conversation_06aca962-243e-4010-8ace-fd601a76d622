import { Router } from 'express';
import { secure } from '../../../middleware/auth';
import { GameController } from '../../../controllers/game/gameController';
import {
  gameUpload,
  handleMulterError,
} from '../../../utils/upload/forumUpload';

export const gameRoute = Router();

// Create a new game
gameRoute.post('/create', secure, GameController.createGame);

// Create a new game with file attachments
gameRoute.post(
  '/create-with-files',
  secure,
  gameUpload.array('files', 3),
  handleMulterError,
  GameController.createGameWithFiles
);

// Get game by ID
gameRoute.get('/:gameId', secure, GameController.getGame);

// Get all games with filters
gameRoute.get('/', secure, GameController.getGames);

// Update game
gameRoute.patch('/:gameId', secure, GameController.updateGame);

// Publish game
gameRoute.patch('/:gameId/publish', secure, GameController.publishGame);

// Start game
gameRoute.patch('/:gameId/start', secure, GameController.startGame);

// Complete game
gameRoute.patch('/:gameId/complete', secure, GameController.completeGame);

// Cancel game
gameRoute.patch('/:gameId/cancel', secure, GameController.cancelGame);

// Delete game
gameRoute.delete('/:gameId', secure, GameController.deleteGame);

// Get my games (created by current user)
gameRoute.get('/my/games', secure, GameController.getMyGames);
