import { Server } from 'http';
import { logger } from '../v1/utils/logger';
import { closeQueues } from '../v1/utils/queue';
import { db } from '../v1/utils/model';
import { stopCronJobs } from '../v1/jobs/cronJobs';

/**
 * Configure graceful shutdown for the application
 * @param server HTTP server instance
 */
export const configureGracefulShutdown = (server: Server): void => {
  async function gracefulShutdown() {
    logger.info('Starting graceful shutdown...');

    server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // Stop all cron jobs
        stopCronJobs();
        logger.info('All cron jobs stopped successfully');

        // Close all Bull queues
        await closeQueues();
        logger.info('All Bull queues closed successfully');

        // Disconnect from database
        await db.$disconnect();
        logger.info('Prisma client disconnected');

        // Exit process
        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    // Add a timeout to force exit if graceful shutdown takes too long
    setTimeout(() => {
      logger.error('Graceful shutdown timed out, forcing exit');
      process.exit(1);
    }, 30000); // 30 seconds timeout
  }

  // Register signal handlers
  process.on('SIGTERM', gracefulShutdown);
  process.on('SIGINT', gracefulShutdown);

  // Handle uncaught exceptions and unhandled rejections
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error);
    // Only shut down in production mode
    if (process.env.NODE_ENV === 'production') {
      gracefulShutdown();
    }
  });

  process.on('unhandledRejection', (reason, promise) => {
    // Log more detailed information about the rejection
    logger.error('Unhandled rejection details:');
    logger.error('Promise:', JSON.stringify(promise, null, 2));
    logger.error('Reason:', reason instanceof Error ? reason.stack : reason);

    // Only shut down in production mode
    if (process.env.NODE_ENV === 'production') {
      gracefulShutdown();
    }
  });

  logger.info('Graceful shutdown handlers configured');
};
