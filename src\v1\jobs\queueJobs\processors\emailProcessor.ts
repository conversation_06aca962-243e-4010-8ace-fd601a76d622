import { getQueue } from '../../../utils/queue';
import { HttpError } from '../../../utils/httpError';
import sendMail from '../../../services/email';
import { Job } from 'bull';
import { logger } from '../../../utils/logger';

// Get the email queue
const emailQueue = getQueue('send-email');

// Set up global error handler for the queue
emailQueue.on('error', (error) => {
  logger.error('Email queue error (global handler):', error);
  // Don't crash the application
});

emailQueue.on('failed', (job, error) => {
  logger.error(`Email job ${job.id} failed (global handler):`, error);
  // Don't crash the application
});

emailQueue.on('stalled', (jobId) => {
  logger.warn(`Email job ${jobId} stalled (global handler)`);
  // Don't crash the application
});

// Process email jobs
emailQueue.process(async (job: Job) => {
  logger.info(`Email job processor started for job ${job.id}`);

  try {
    const { mailOptions } = job.data;

    // Validate mailOptions
    if (!mailOptions || !mailOptions.to || !mailOptions.subject) {
      logger.error(
        `Invalid mail options for job ${job.id}:`,
        JSON.stringify(mailOptions)
      );
      return { success: false, error: 'Invalid mail options' };
    }

    // Add retry logic with backoff
    let attempts = 0;
    const maxAttempts = 3;
    let lastError = null;

    while (attempts < maxAttempts) {
      try {
        // Try to send the email
        await sendMail(mailOptions);
        logger.info(
          `Email sent successfully for job ${job.id} on attempt ${attempts + 1}`
        );
        return { success: true };
      } catch (error) {
        lastError = error;
        attempts++;
        logger.warn(
          `Email sending attempt ${attempts}/${maxAttempts} failed for job ${job.id}:`,
          error
        );

        if (attempts < maxAttempts) {
          // Wait before retrying (exponential backoff)
          const backoffMs = Math.pow(2, attempts) * 1000;
          logger.info(`Retrying in ${backoffMs}ms...`);
          await new Promise((resolve) => setTimeout(resolve, backoffMs));
        }
      }
    }

    // If we get here, all attempts failed
    logger.error(
      `All ${maxAttempts} attempts to send email for job ${job.id} failed`
    );

    // Return failure status instead of throwing an error
    return {
      success: false,
      error:
        lastError instanceof Error
          ? lastError.message
          : 'Unknown error after multiple attempts',
      attempts,
    };
  } catch (error) {
    // Catch any other errors in the processor itself
    logger.error(
      `Unexpected error in email processor for job ${job.id}:`,
      error
    );

    // Return failure status instead of throwing an error
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Unexpected processor error',
    };
  }
});
