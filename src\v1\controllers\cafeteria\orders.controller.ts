import { Request, Response } from 'express';
import { orderService } from '../../services/cafeteria/orders';
import { controllerOperations } from '../handlers/handleController';

const staffPayment = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.checkStaffPayment, req.body, res, staffId);
};

const createOrder = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.createOrder, req.body, res, staffId);
};

export const getOrderById = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { id } = req.params;
  controllerOperations(orderService.getOrderById, id, res, staffId);
};

const getAllOrders = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.getAllOrders, req.query, res, staffId);
};

const getOrderStats = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.orderStats, undefined, res, staffId);
};

export const ordersController = {
  staffPayment,
  getOrderById,
  createOrder,
  getAllOrders,
  getOrderStats
};
