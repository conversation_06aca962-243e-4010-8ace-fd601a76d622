import { Request, Response } from 'express';
import { GameService } from '../../services/game/gameService';
import { FileUploadService } from '../../services/upload/fileUploadService';
import { convertMulterFiles } from '../../utils/upload/forumUpload';
import { logger } from '../../utils/logger';

export class GameController {
  // Create a new game
  static async createGame(req: Request, res: Response) {
    try {
      const {
        title,
        description,
        gameType,
        startDate,
        endDate,
        maxParticipants,
        entryFee,
        prizePool,
        rules,
        locationId,
        groupId,
        questions,
        rewards,
      } = req.body;
      const createdById = req.Staff as unknown as number;

      if (!createdById) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (
        !title ||
        !description ||
        !gameType ||
        !startDate ||
        !endDate ||
        !prizePool
      ) {
        res.status(400).json({
          success: false,
          message:
            'Title, description, game type, start date, end date, and prize pool are required',
        });
      }

      if (!questions || !Array.isArray(questions) || questions.length === 0) {
        res.status(400).json({
          success: false,
          message: 'At least one question is required',
        });
      }

      if (!rewards || !Array.isArray(rewards) || rewards.length === 0) {
        res.status(400).json({
          success: false,
          message: 'At least one reward is required',
        });
      }

      const game = await GameService.createGame({
        title,
        description,
        gameType,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        maxParticipants,
        entryFee,
        prizePool,
        rules,
        createdById,
        locationId,
        groupId,
        questions,
        rewards,
      });

      res.status(201).json({
        success: true,
        message: 'Game created successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error creating game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to create game',
      });
    }
  }

  // Create a new game with file attachments
  static async createGameWithFiles(req: Request, res: Response) {
    try {
      const {
        title,
        description,
        gameType,
        startDate,
        endDate,
        maxParticipants,
        entryFee,
        prizePool,
        rules,
        locationId,
        groupId,
        questions,
        rewards,
      } = req.body;
      const createdById = req.Staff as unknown as number;
      const files = req.files as Express.Multer.File[];

      if (!createdById) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (
        !title ||
        !description ||
        !gameType ||
        !startDate ||
        !endDate ||
        !prizePool
      ) {
        res.status(400).json({
          success: false,
          message:
            'Title, description, game type, start date, end date, and prize pool are required',
        });
      }

      let attachments = undefined;

      // Handle file uploads if files are provided
      if (files && files.length > 0) {
        try {
          const fileData = convertMulterFiles(files);
          const uploadedFiles = await FileUploadService.uploadMultipleFiles(
            fileData,
            'games',
            {
              useCloudinary: true,
              maxSize: 5 * 1024 * 1024, // 5MB for game content
              allowedTypes: [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'application/pdf',
                'text/plain',
              ],
            }
          );

          attachments = uploadedFiles.map((file) => ({
            fileName: file.fileName,
            fileUrl: file.fileUrl,
            fileType: file.fileType,
            fileSize: file.fileSize,
          }));
        } catch (uploadError) {
          logger.error('Error uploading game files:', uploadError);
          res.status(400).json({
            success: false,
            message:
              uploadError instanceof Error
                ? uploadError.message
                : 'Failed to upload files',
          });
        }
      }

      const game = await GameService.createGame({
        title,
        description,
        gameType,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        maxParticipants: maxParticipants
          ? parseInt(maxParticipants)
          : undefined,
        entryFee: entryFee ? parseFloat(entryFee) : 0,
        prizePool: parseFloat(prizePool),
        rules,
        locationId: locationId ? parseInt(locationId) : undefined,
        groupId: groupId ? parseInt(groupId) : undefined,
        createdById,
        questions: JSON.parse(questions),
        rewards: JSON.parse(rewards),
        attachments,
      });

      res.status(201).json({
        success: true,
        message: 'Game with attachments created successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error creating game with files:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to create game',
      });
    }
  }

  // Get game by ID
  static async getGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const userId = req.Staff as unknown as number;

      const game = await GameService.getGameById(gameId, userId);

      res.status(200).json({
        success: true,
        data: game,
      });
    } catch (error) {
      logger.error('Error getting game:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get game',
      });
    }
  }

  // Get all games with filters
  static async getGames(req: Request, res: Response) {
    try {
      const {
        status,
        gameType,
        locationId,
        groupId,
        createdById,
        page = 1,
        limit = 20,
      } = req.query;

      const filters: any = {};
      if (status) filters.status = status;
      if (gameType) filters.gameType = gameType;
      if (locationId) filters.locationId = parseInt(locationId as string);
      if (groupId) filters.groupId = parseInt(groupId as string);
      if (createdById) filters.createdById = parseInt(createdById as string);
      filters.page = parseInt(page as string);
      filters.limit = parseInt(limit as string);

      const result = await GameService.getGames(filters);

      res.status(200).json({
        success: true,
        data: result.games,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting games:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get games',
      });
    }
  }

  // Update game
  static async updateGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const updateData = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // Convert date strings to Date objects if provided
      if (updateData.startDate) {
        updateData.startDate = new Date(updateData.startDate);
      }
      if (updateData.endDate) {
        updateData.endDate = new Date(updateData.endDate);
      }

      const game = await GameService.updateGame(gameId, updateData, userId);

      res.status(200).json({
        success: true,
        message: 'Game updated successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error updating game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to update game',
      });
    }
  }

  // Publish game
  static async publishGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const game = await GameService.publishGame(gameId, userId);

      res.status(200).json({
        success: true,
        message: 'Game published successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error publishing game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to publish game',
      });
    }
  }

  // Start game
  static async startGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const game = await GameService.startGame(gameId, userId);

      res.status(200).json({
        success: true,
        message: 'Game started successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error starting game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to start game',
      });
    }
  }

  // Complete game
  static async completeGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const game = await GameService.completeGame(gameId, userId);

      res.status(200).json({
        success: true,
        message: 'Game completed successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error completing game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to complete game',
      });
    }
  }

  // Cancel game
  static async cancelGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const { reason } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const game = await GameService.cancelGame(gameId, userId, reason);

      res.status(200).json({
        success: true,
        message: 'Game cancelled successfully',
        data: game,
      });
    } catch (error) {
      logger.error('Error cancelling game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to cancel game',
      });
    }
  }

  // Delete game
  static async deleteGame(req: Request, res: Response) {
    try {
      const { gameId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await GameService.deleteGame(gameId, userId);

      res.status(200).json({
        success: true,
        message: 'Game deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting game:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to delete game',
      });
    }
  }

  // Get my games (created by current user)
  static async getMyGames(req: Request, res: Response) {
    try {
      const { status, page = 1, limit = 20 } = req.query;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const filters: any = {
        createdById: userId,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      if (status) filters.status = status;

      const result = await GameService.getGames(filters);

      res.status(200).json({
        success: true,
        data: result.games,
        pagination: result.pagination,
      });
    } catch (error) {
      logger.error('Error getting my games:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get your games',
      });
    }
  }
}
