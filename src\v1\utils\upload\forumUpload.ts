import multer from 'multer';
import path from 'path';
import { Request } from 'express';

// Configure storage
const storage = multer.memoryStorage(); // Use memory storage for better control

// File filter for forum uploads
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Allowed MIME types for forum uploads
  const allowedTypes = [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    // Audio
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    'audio/mp4',
    // Video
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/quicktime',
  ];

  // Allowed file extensions
  const allowedExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.txt',
    '.mp3',
    '.wav',
    '.ogg',
    '.m4a',
    '.mp4',
    '.webm',
    '.mov',
  ];

  const ext = path.extname(file.originalname).toLowerCase();

  if (allowedTypes.includes(file.mimetype) && allowedExtensions.includes(ext)) {
    cb(null, true);
  } else {
    cb(
      new Error(
        `File type not allowed. Allowed types: ${allowedExtensions.join(', ')}`
      )
    );
  }
};

// Multer configuration for forum uploads
export const forumUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5, // Maximum 5 files per upload
  },
});

// Multer configuration for game content uploads
export const gameUpload = multer({
  storage,
  fileFilter: (
    req: Request,
    file: Express.Multer.File,
    cb: multer.FileFilterCallback
  ) => {
    // More restrictive for game content - mainly images and documents
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
    ];

    const allowedExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.pdf',
      '.txt',
    ];

    const ext = path.extname(file.originalname).toLowerCase();

    if (
      allowedTypes.includes(file.mimetype) &&
      allowedExtensions.includes(ext)
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          `File type not allowed for game content. Allowed types: ${allowedExtensions.join(', ')}`
        )
      );
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit for game content
    files: 3, // Maximum 3 files per upload
  },
});

// Multer configuration for avatar uploads
export const avatarUpload = multer({
  storage,
  fileFilter: (
    req: Request,
    file: Express.Multer.File,
    cb: multer.FileFilterCallback
  ) => {
    // Only images for avatars
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];

    const ext = path.extname(file.originalname).toLowerCase();

    if (
      allowedTypes.includes(file.mimetype) &&
      allowedExtensions.includes(ext)
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG, PNG, and WebP images are allowed for avatars'));
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit for avatars
    files: 1, // Only one avatar file
  },
});

// Error handler for multer errors
export const handleMulterError = (
  error: any,
  req: Request,
  res: any,
  next: any
) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: 'File size too large',
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Too many files uploaded',
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Unexpected file field',
        });
      default:
        return res.status(400).json({
          success: false,
          message: 'File upload error',
        });
    }
  } else if (error) {
    return res.status(400).json({
      success: false,
      message: error.message || 'File upload error',
    });
  }
  next();
};

// Utility function to convert multer file to FileUploadData
export const convertMulterFile = (file: Express.Multer.File) => ({
  originalName: file.originalname,
  mimetype: file.mimetype,
  size: file.size,
  buffer: file.buffer,
});

// Utility function to convert multiple multer files
export const convertMulterFiles = (files: Express.Multer.File[]) =>
  files.map(convertMulterFile);
