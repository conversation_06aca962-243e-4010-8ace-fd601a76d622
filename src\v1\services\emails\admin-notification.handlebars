<html
  xmlns='http://www.w3.org/1999/xhtml'
  xmlns:v='urn:schemas-microsoft-com:vml'
  xmlns:o='urn:schemas-microsoft-com:office:office'
>
  <head>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8' />
    <meta http-equiv='X-UA-Compatible' content='IE=edge' />
    <meta name='format-detection' content='telephone=no' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>New Package Booking Notification</title>
    <style type='text/css' emogrify='no'>
      #outlook a {
        padding: 0;
      }
      .ExternalClass {
        width: 100%;
      }
      .ExternalClass,
      .ExternalClass p,
      .ExternalClass span,
      .ExternalClass font,
      .ExternalClass td,
      .ExternalClass div {
        line-height: 100%;
      }
      table td {
        border-collapse: collapse;
        mso-line-height-rule: exactly;
      }
      .editable.image {
        font-size: 0 !important;
        line-height: 0 !important;
      }
      body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        -ms-interpolation-mode: bicubic;
      }
      a img {
        border: none;
      }
      .ExternalClass * {
        line-height: 100%;
      }
      .appleBody a {
        color: #68440a;
        text-decoration: none;
      }
      .appleFooter a {
        color: #999999;
        text-decoration: none;
      }
      td[class='bodyCell'] {
        padding-top: 10px;
        padding-right: 10px;
        padding-bottom: 10px;
        padding-left: 10px;
      }
    </style>
    <style type='text/css'>
      .urgent-badge {
        background-color: #ff3b30;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 15px;
      }
      .urgent-header {
        background-color: #ff3b30 !important;
      }
      .priority-box {
        background-color: #fff9f9;
        border: 2px solid #ff3b30;
        border-left: 8px solid #ff3b30;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
      }
      .action-button {
        background-color: #3f3d56;
        color: white;
        padding: 12px 25px;
        text-decoration: none;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
        margin: 15px 0;
      }
      .action-button:hover {
        background-color: #2e2c40;
      }
      @media only screen and (max-width: 480px) {
        .container {
          width: 100% !important;
        }
        .footer {
          width: auto !important;
          margin-left: 0;
        }
        .mobile-hidden {
          display: none !important;
        }
        .logo {
          display: block !important;
          padding: 0 !important;
        }
        img {
          max-width: 100% !important;
          height: auto !important;
          max-height: auto !important;
        }
        .header img {
          max-width: 100% !important;
          height: auto !important;
          max-height: auto !important;
        }
        .photo img {
          width: 100% !important;
          max-width: 100% !important;
          height: auto !important;
        }
        .drop {
          display: block !important;
          width: 100% !important;
          float: left;
          clear: both;
        }
        .footerlogo {
          display: block !important;
          width: 100% !important;
          padding-top: 15px;
          float: left;
          clear: both;
        }
        .nav4,
        .nav5,
        .nav6 {
          display: none !important;
        }
        .tableBlock {
          width: 100% !important;
        }
        .responsive-td {
          width: 100% !important;
          display: block !important;
          padding: 0 !important;
        }
        .fluid,
        .fluid-centered {
          width: 100% !important;
          max-width: 100% !important;
          height: auto !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }
        .fluid-centered {
          margin-left: auto !important;
          margin-right: auto !important;
        }
        body {
          -webkit-text-size-adjust: none;
          -ms-text-size-adjust: none;
        }
      }
    </style>
  </head>
  <body
    style='margin: 0; padding: 0; background-color: #f2f2f2; color: #333333; font-family: Arial, sans-serif;'
  >
    <table
      cellpadding='0'
      cellspacing='0'
      width='100%'
      style='min-width: 100%; background-color: #f2f2f2;'
    >
      <tr>
        <td valign='top' align='center'>
          <table
            cellpadding='0'
            cellspacing='0'
            width='600'
            class='container'
            style='max-width: 600px; margin: 0 auto; background-color: #ffffff;'
          >
            <!-- Header -->
            <tr>
              <td
                style='padding: 20px; text-align: center; background-color: #ff3b30;'
                class='urgent-header'
              >
                <h1 style='color: #ffffff; margin: 0;'>URGENT: New Package
                  Booking</h1>
              </td>
            </tr>
            <!-- Content -->
            <tr>
              <td style='padding: 20px;'>
                <div class='urgent-badge'>URGENT ACTION REQUIRED</div>
                <p style='margin-top: 0; margin-bottom: 20px;'><strong>Hello
                    Admin,</strong></p>
                <div class='priority-box'>
                  <p style='margin-top: 0; margin-bottom: 10px;'><strong>A new
                      package booking has been completed and requires your
                      immediate attention.</strong></p>
                  <p style='margin-top: 0; margin-bottom: 0;'>Please review and
                    process this booking as soon as possible to ensure timely
                    service delivery.</p>
                </div>
                <table
                  cellpadding='0'
                  cellspacing='0'
                  width='100%'
                  style='border-collapse: collapse; margin-bottom: 20px;'
                >
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; width: 40%; font-weight: bold;'
                    >Package Name:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{package}}</td>
                  </tr>
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; font-weight: bold;'
                    >Customer Name:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{customerName}}</td>
                  </tr>
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; font-weight: bold;'
                    >Customer Email:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{customerEmail}}</td>
                  </tr>
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; font-weight: bold;'
                    >Booking Reference:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{bookingRef}}</td>
                  </tr>
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; font-weight: bold;'
                    >Amount:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{amount}}</td>
                  </tr>
                  <tr>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd; font-weight: bold;'
                    >Date:</td>
                    <td
                      style='padding: 10px; border: 1px solid #dddddd;'
                    >{{date}}</td>
                  </tr>
                </table>

                <div style='text-align: center; margin: 30px 0;'>
                  <a href='{{dashboardUrl}}' class='action-button'>PROCESS THIS
                    BOOKING NOW</a>
                </div>

                <div class='priority-box' style='margin-top: 20px;'>
                  <p style='margin-top: 0; margin-bottom: 10px;'><strong>⚠️
                      Time-Sensitive:</strong>
                    This booking requires prompt attention to maintain our
                    service standards.</p>
                  <p style='margin-top: 0; margin-bottom: 0;'>Customers expect
                    timely processing of their health packages. Please
                    prioritize this task.</p>
                </div>

                <p style='margin-top: 20px; margin-bottom: 20px;'>Thank you for
                  your immediate attention to this matter,<br /><strong
                  >Cedarcrest Hospitals Innovations</strong></p>
              </td>
            </tr>
            <!-- Footer -->
            <tr>
              <td
                style='padding: 20px; text-align: center; background-color: #f5f5f5; color: #777777; font-size: 12px;'
              >
                <p style='margin-top: 0; margin-bottom: 10px;'>This is an
                  automated notification. Please do not reply to this email.</p>
                <p style='margin-top: 0; margin-bottom: 0;'>&copy; 2025
                  Cedarcrest Hospitals Innovations. All rights reserved.</p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>