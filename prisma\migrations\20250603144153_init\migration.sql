-- CreateEnum
CREATE TYPE "ReferralRewardLogType" AS ENUM ('POINTS_AWARDED', 'COMMISSION_EARNED');

-- CreateEnum
CREATE TYPE "RewardType" AS ENUM ('PERCENTAGE', 'POINTS', 'CASH');

-- C<PERSON><PERSON>num
CREATE TYPE "PackageBookingStatus" AS ENUM ('DRAFT', 'PENDING', 'COMPLETED');

-- C<PERSON><PERSON>num
CREATE TYPE "TransactionStatus" AS ENUM ('PENDING', 'CONFIRMED', 'FAILED');

-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('INBOUND', 'OUTBOUND');

-- Create<PERSON>num
CREATE TYPE "StaffType" AS ENUM ('FULL', 'CONTRACT', 'INTERN');

-- CreateEnum
CREATE TYPE "RoleType" AS ENUM ('USER', 'STAFF', 'ADMIN');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "ReferralStatus" AS ENUM ('PENDING_REVIEW', 'ACCEPTED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ReferringEntityType" AS ENUM ('INDIVIDUAL_DOCTOR', 'ORGANIZATION');

-- CreateEnum
CREATE TYPE "VisitingConsultantStatus" AS ENUM ('NOT_APPLICABLE', 'APPLICATION_STARTED', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'ONBOARDED');

-- CreateEnum
CREATE TYPE "CredentialDocumentType" AS ENUM ('MEDICAL_LICENSE', 'CV_RESUME', 'CERTIFICATION', 'IDENTIFICATION', 'OTHER');

-- CreateEnum
CREATE TYPE "CredentialVerificationStatus" AS ENUM ('PENDING', 'VERIFIED', 'REJECTED', 'INFORMATION_REQUESTED');

-- CreateTable
CREATE TABLE "Admin" (
    "admin_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email_address" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,
    "designation" TEXT NOT NULL,
    "last_login" TIMESTAMP(3),
    "reset_password_token" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "date_reset_password_request" TIMESTAMP(3),
    "date_reset_password" TIMESTAMP(3),
    "location_id" INTEGER,
    "role_id" INTEGER NOT NULL,

    CONSTRAINT "Admin_pkey" PRIMARY KEY ("admin_id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Permission" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uhid" TEXT,
    "title" TEXT,
    "email_address" TEXT NOT NULL,
    "gender" TEXT,
    "phone_number" TEXT,
    "date_of_birth" TIMESTAMP(3),
    "first_name" TEXT,
    "last_name" TEXT,
    "residential_address" TEXT,
    "next_of_kin" TEXT,
    "next_of_kin_phone" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("user_id")
);

-- CreateTable
CREATE TABLE "package_category" (
    "package_category_id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "slug" TEXT,
    "created_by" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "updated_by" TEXT,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "package_category_pkey" PRIMARY KEY ("package_category_id")
);

-- CreateTable
CREATE TABLE "Package" (
    "package_id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "created_by" TEXT NOT NULL,
    "description" TEXT,
    "base_price" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "package_status" BOOLEAN NOT NULL DEFAULT false,
    "total_slot" INTEGER NOT NULL DEFAULT 0,
    "slug" TEXT NOT NULL,
    "package_image" TEXT NOT NULL,
    "package_category_id" INTEGER NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "Package_pkey" PRIMARY KEY ("package_id")
);

-- CreateTable
CREATE TABLE "Update" (
    "package_id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "item_updated" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Update_pkey" PRIMARY KEY ("package_id")
);

-- CreateTable
CREATE TABLE "Location" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT,
    "region" TEXT,
    "country" TEXT,
    "currency" TEXT NOT NULL DEFAULT 'NGN',
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Test" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,

    CONSTRAINT "Test_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "package_location_price" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "currency" TEXT NOT NULL DEFAULT 'NGN',
    "start_date" TIMESTAMP(3),
    "end_date" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(3),
    "package_id" INTEGER NOT NULL,
    "location_id" INTEGER NOT NULL,

    CONSTRAINT "package_location_price_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "package_price_modifier" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "modifier_code" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "start_date" TIMESTAMP(3),
    "end_date" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "percentage" DOUBLE PRECISION,
    "description" TEXT,

    CONSTRAINT "package_price_modifier_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "discount_record" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "package" TEXT NOT NULL,
    "discount_amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,

    CONSTRAINT "discount_record_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "package_booking" (
    "package_booking_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "user_id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "location" TEXT,
    "total_amount" DECIMAL(10,2) DEFAULT 0.0,
    "package_id" INTEGER NOT NULL,
    "package_location_id" INTEGER NOT NULL,
    "referral_code" TEXT,
    "package_booking_status" "PackageBookingStatus" NOT NULL DEFAULT 'DRAFT',
    "bookingRef" TEXT NOT NULL,
    "voucher_code" TEXT,
    "voucher_discount" DECIMAL(10,2) DEFAULT 0.0,
    "updated_by" TEXT,

    CONSTRAINT "package_booking_pkey" PRIMARY KEY ("package_booking_id")
);

-- CreateTable
CREATE TABLE "Transaction" (
    "transaction_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "charges" DECIMAL(10,2) DEFAULT 0.0,
    "balance" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "transaction_reference" TEXT NOT NULL,
    "transaction_status" "TransactionStatus" NOT NULL DEFAULT 'PENDING',
    "transaction_mode" TEXT NOT NULL,
    "remarks" TEXT,
    "transaction_type" "TransactionType" NOT NULL,
    "role" "RoleType" NOT NULL,
    "user_id" TEXT,
    "staff_id" INTEGER,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("transaction_id")
);

-- CreateTable
CREATE TABLE "Staff" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "password" TEXT,
    "locationId" INTEGER NOT NULL,
    "role" TEXT,
    "email" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "staff_code" TEXT,
    "type" "StaffType" NOT NULL DEFAULT 'FULL',
    "full_name" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "wallet" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "departmentId" INTEGER NOT NULL,
    "unitId" INTEGER,
    "is_doctor" BOOLEAN NOT NULL DEFAULT false,
    "is_consultant" BOOLEAN NOT NULL DEFAULT false,
    "is_visiting_consultant" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Staff_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Department" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "managerId" INTEGER,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Unit" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "departmentId" INTEGER NOT NULL,
    "headId" INTEGER,

    CONSTRAINT "Unit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_code" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "code" TEXT NOT NULL,
    "assigned_to_staff_id" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "referral_code_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_code_usage" (
    "id" SERIAL NOT NULL,
    "referral_code_id" INTEGER NOT NULL,
    "date_used" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "purpose" TEXT NOT NULL,
    "value" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "redeemed" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "referral_code_usage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Reward" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "type" "RewardType" NOT NULL,
    "name" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "deactivated" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Reward_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Feedback" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "name" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "feedback" TEXT NOT NULL,

    CONSTRAINT "Feedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "system_settings" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "system_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referring_entities" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "entity_type" "ReferringEntityType" NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "phone_number" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "specialty" TEXT,
    "medical_license_number" TEXT,
    "organization_address" TEXT,
    "primary_contact_name" TEXT,
    "primary_contact_email" TEXT,
    "primary_contact_phone" TEXT,
    "reward_id" INTEGER NOT NULL,
    "visiting_consultant_status" "VisitingConsultantStatus",
    "consultant_application_date" TIMESTAMP(3),

    CONSTRAINT "referring_entities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "external_referrals" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "patient_first_name" TEXT NOT NULL,
    "patient_last_name" TEXT NOT NULL,
    "patient_date_of_birth" TIMESTAMP(3),
    "patient_phone_number" TEXT,
    "patient_email_address" TEXT,
    "referral_date" TIMESTAMP(3) NOT NULL,
    "reason_for_referral" TEXT NOT NULL,
    "internal_doctor_id" INTEGER NOT NULL,
    "referring_entity_id" TEXT NOT NULL,
    "status" "ReferralStatus" NOT NULL DEFAULT 'PENDING_REVIEW',
    "notes" TEXT,

    CONSTRAINT "external_referrals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_reward_logs" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "referring_entity_id" TEXT NOT NULL,
    "external_referral_id" INTEGER NOT NULL,
    "log_type" "ReferralRewardLogType" NOT NULL,
    "points_awarded" INTEGER,
    "commission_amount" DECIMAL(10,2),
    "triggering_bill_amount" DECIMAL(10,2),
    "notes" TEXT,

    CONSTRAINT "referral_reward_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "credential_documents" (
    "id" TEXT NOT NULL,
    "referring_entity_id" TEXT NOT NULL,
    "document_type" "CredentialDocumentType" NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_url" TEXT NOT NULL,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "verification_status" "CredentialVerificationStatus" NOT NULL DEFAULT 'PENDING',
    "verified_at" TIMESTAMP(3),
    "verifier_notes" TEXT,

    CONSTRAINT "credential_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PermissionToRole" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_PermissionToRole_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_PackageToTest" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_PackageToTest_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_PackageLocationPriceToPackagePriceModifier" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_PackageLocationPriceToPackagePriceModifier_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_ReferralCodeToReward" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_ReferralCodeToReward_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "Admin_email_address_key" ON "Admin"("email_address");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Permission_name_key" ON "Permission"("name");

-- CreateIndex
CREATE UNIQUE INDEX "User_uhid_key" ON "User"("uhid");

-- CreateIndex
CREATE UNIQUE INDEX "package_category_name_key" ON "package_category"("name");

-- CreateIndex
CREATE INDEX "package_category_slug_idx" ON "package_category"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Package_slug_key" ON "Package"("slug");

-- CreateIndex
CREATE INDEX "Package_slug_idx" ON "Package"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Test_name_key" ON "Test"("name");

-- CreateIndex
CREATE UNIQUE INDEX "package_price_modifier_modifier_code_key" ON "package_price_modifier"("modifier_code");

-- CreateIndex
CREATE UNIQUE INDEX "Staff_staff_code_key" ON "Staff"("staff_code");

-- CreateIndex
CREATE UNIQUE INDEX "Department_name_key" ON "Department"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Department_managerId_key" ON "Department"("managerId");

-- CreateIndex
CREATE UNIQUE INDEX "Unit_name_key" ON "Unit"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Unit_headId_key" ON "Unit"("headId");

-- CreateIndex
CREATE UNIQUE INDEX "referral_code_code_key" ON "referral_code"("code");

-- CreateIndex
CREATE UNIQUE INDEX "referral_code_assigned_to_staff_id_key" ON "referral_code"("assigned_to_staff_id");

-- CreateIndex
CREATE UNIQUE INDEX "system_settings_key_key" ON "system_settings"("key");

-- CreateIndex
CREATE UNIQUE INDEX "referring_entities_email_key" ON "referring_entities"("email");

-- CreateIndex
CREATE UNIQUE INDEX "referring_entities_medical_license_number_key" ON "referring_entities"("medical_license_number");

-- CreateIndex
CREATE INDEX "_PermissionToRole_B_index" ON "_PermissionToRole"("B");

-- CreateIndex
CREATE INDEX "_PackageToTest_B_index" ON "_PackageToTest"("B");

-- CreateIndex
CREATE INDEX "_PackageLocationPriceToPackagePriceModifier_B_index" ON "_PackageLocationPriceToPackagePriceModifier"("B");

-- CreateIndex
CREATE INDEX "_ReferralCodeToReward_B_index" ON "_ReferralCodeToReward"("B");

-- AddForeignKey
ALTER TABLE "Admin" ADD CONSTRAINT "Admin_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "Location"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Admin" ADD CONSTRAINT "Admin_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Package" ADD CONSTRAINT "Package_package_category_id_fkey" FOREIGN KEY ("package_category_id") REFERENCES "package_category"("package_category_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "package_location_price" ADD CONSTRAINT "package_location_price_package_id_fkey" FOREIGN KEY ("package_id") REFERENCES "Package"("package_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "package_location_price" ADD CONSTRAINT "package_location_price_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "discount_record" ADD CONSTRAINT "discount_record_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "package_booking" ADD CONSTRAINT "package_booking_package_id_fkey" FOREIGN KEY ("package_id") REFERENCES "Package"("package_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "package_booking" ADD CONSTRAINT "package_booking_package_location_id_fkey" FOREIGN KEY ("package_location_id") REFERENCES "package_location_price"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "package_booking" ADD CONSTRAINT "package_booking_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("user_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_staff_id_fkey" FOREIGN KEY ("staff_id") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "Unit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Department" ADD CONSTRAINT "Department_managerId_fkey" FOREIGN KEY ("managerId") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Unit" ADD CONSTRAINT "Unit_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Unit" ADD CONSTRAINT "Unit_headId_fkey" FOREIGN KEY ("headId") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_code" ADD CONSTRAINT "referral_code_assigned_to_staff_id_fkey" FOREIGN KEY ("assigned_to_staff_id") REFERENCES "Staff"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_code_usage" ADD CONSTRAINT "referral_code_usage_referral_code_id_fkey" FOREIGN KEY ("referral_code_id") REFERENCES "referral_code"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referring_entities" ADD CONSTRAINT "referring_entities_reward_id_fkey" FOREIGN KEY ("reward_id") REFERENCES "Reward"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "external_referrals" ADD CONSTRAINT "external_referrals_internal_doctor_id_fkey" FOREIGN KEY ("internal_doctor_id") REFERENCES "Staff"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "external_referrals" ADD CONSTRAINT "external_referrals_referring_entity_id_fkey" FOREIGN KEY ("referring_entity_id") REFERENCES "referring_entities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_reward_logs" ADD CONSTRAINT "referral_reward_logs_referring_entity_id_fkey" FOREIGN KEY ("referring_entity_id") REFERENCES "referring_entities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_reward_logs" ADD CONSTRAINT "referral_reward_logs_external_referral_id_fkey" FOREIGN KEY ("external_referral_id") REFERENCES "external_referrals"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_referring_entity_id_fkey" FOREIGN KEY ("referring_entity_id") REFERENCES "referring_entities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PermissionToRole" ADD CONSTRAINT "_PermissionToRole_A_fkey" FOREIGN KEY ("A") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PermissionToRole" ADD CONSTRAINT "_PermissionToRole_B_fkey" FOREIGN KEY ("B") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PackageToTest" ADD CONSTRAINT "_PackageToTest_A_fkey" FOREIGN KEY ("A") REFERENCES "Package"("package_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PackageToTest" ADD CONSTRAINT "_PackageToTest_B_fkey" FOREIGN KEY ("B") REFERENCES "Test"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PackageLocationPriceToPackagePriceModifier" ADD CONSTRAINT "_PackageLocationPriceToPackagePriceModifier_A_fkey" FOREIGN KEY ("A") REFERENCES "package_location_price"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PackageLocationPriceToPackagePriceModifier" ADD CONSTRAINT "_PackageLocationPriceToPackagePriceModifier_B_fkey" FOREIGN KEY ("B") REFERENCES "package_price_modifier"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ReferralCodeToReward" ADD CONSTRAINT "_ReferralCodeToReward_A_fkey" FOREIGN KEY ("A") REFERENCES "referral_code"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ReferralCodeToReward" ADD CONSTRAINT "_ReferralCodeToReward_B_fkey" FOREIGN KEY ("B") REFERENCES "Reward"("id") ON DELETE CASCADE ON UPDATE CASCADE;
