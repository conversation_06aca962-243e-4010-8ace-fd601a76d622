import { Response } from 'express';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';

type OperationHandler<T> = (
  userId: any,
  params: any,
  reqBody: any,
  query?: any
) => Promise<T>;

export const controllerOperations = async <T>(
  handler: OperationHandler<T>,
  params: any,
  res: Response,
  userId?: string | number,
  reqBody?: any,
  query?: any
) => {
  try {
    const result = await handler(userId, params, query, reqBody);
    return res.status(200).json({ message: 'Successful', data: result });
  } catch (error) {
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        message: error.message,
        data: null,
      });
    } else if (error instanceof Error) {
      return res.status(400).json({ message: error.message, data: null });
    } else {
      logger.error('An unknown error occurred');
    }

    return res.global;
  }
};
