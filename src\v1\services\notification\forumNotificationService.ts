import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import { messagingService } from '../messaging';
import { sendToUser, sendToChannel, sendToGroup } from '../socket';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import config from '../../../config/app.config';

const prisma = new PrismaClient();

export interface NotificationData {
  type:
    | 'forum_message'
    | 'forum_mention'
    | 'forum_group_invite'
    | 'forum_channel_created'
    | 'forum_member_joined'
    | 'forum_member_left';
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high';
}

export class ForumNotificationService {
  // Send notification for new forum message
  static async notifyNewMessage(
    channelId: number,
    messageId: string,
    authorId: number,
    content: string,
    mentions?: number[]
  ) {
    try {
      // Get channel and group information
      const channel = await prisma.forumChannel.findUnique({
        where: { id: channelId },
        include: {
          group: {
            include: {
              members: {
                include: {
                  staff: {
                    select: {
                      id: true,
                      fullName: true,
                      email: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!channel) {
        throw new Error('Channel not found');
      }

      const author = await prisma.staff.findUnique({
        where: { id: authorId },
        select: {
          id: true,
          fullName: true,
        },
      });

      if (!author) {
        throw new Error('Author not found');
      }

      const authorName = `${author.fullName}`;

      // Send real-time notification to channel members
      sendToChannel(channelId, 'new_message', {
        messageId,
        channelId,
        groupId: channel.group.id,
        author: {
          id: authorId,
          name: authorName,
        },
        content:
          content.substring(0, 100) + (content.length > 100 ? '...' : ''),
        timestamp: new Date(),
      });

      // Handle mentions
      if (mentions && mentions.length > 0) {
        await this.notifyMentions(
          mentions,
          authorName,
          channel.name,
          channel.group.name,
          content,
          messageId
        );
      }

      // Send notifications to group members (excluding author)
      const groupMembers = channel.group.members.filter(
        (member) => member.staffId !== authorId
      );

      for (const member of groupMembers) {
        // Skip if user was mentioned (already notified)
        if (mentions && mentions.includes(member.staffId)) {
          continue;
        }

        const notificationData: NotificationData = {
          type: 'forum_message',
          title: `New message in ${channel.name}`,
          message: `${authorName} posted in ${channel.group.name} > ${channel.name}`,
          data: {
            channelId,
            groupId: channel.group.id,
            messageId,
            authorId,
            authorName,
          },
          priority: 'low',
        };

        await this.sendNotification(member.staffId, notificationData);
      }

      logger.info(`Forum message notifications sent for message ${messageId}`);
    } catch (error) {
      logger.error('Error sending forum message notifications:', error);
      throw error;
    }
  }

  // Send notification for user mentions
  static async notifyMentions(
    mentionedUserIds: number[],
    authorName: string,
    channelName: string,
    groupName: string,
    content: string,
    messageId: string
  ) {
    try {
      for (const userId of mentionedUserIds) {
        const notificationData: NotificationData = {
          type: 'forum_mention',
          title: `You were mentioned in ${channelName}`,
          message: `${authorName} mentioned you in ${groupName} > ${channelName}`,
          data: {
            messageId,
            authorName,
            channelName,
            groupName,
            content: content.substring(0, 200),
          },
          priority: 'high',
        };

        await this.sendNotification(userId, notificationData);

        // Send real-time mention notification
        sendToUser(userId, 'user_mentioned', {
          messageId,
          authorName,
          channelName,
          groupName,
          content: content.substring(0, 100),
          timestamp: new Date(),
        });

        // Send email notification for mentions
        await this.sendMentionEmail(
          userId,
          authorName,
          channelName,
          groupName,
          content
        );
      }

      logger.info(
        `Mention notifications sent to ${mentionedUserIds.length} users`
      );
    } catch (error) {
      logger.error('Error sending mention notifications:', error);
      throw error;
    }
  }

  // Send notification for group invitations
  static async notifyGroupInvite(
    invitedUserId: number,
    inviterName: string,
    groupName: string,
    groupId: number
  ) {
    try {
      const notificationData: NotificationData = {
        type: 'forum_group_invite',
        title: `Invitation to join ${groupName}`,
        message: `${inviterName} invited you to join the group "${groupName}"`,
        data: {
          groupId,
          groupName,
          inviterName,
        },
        priority: 'medium',
      };

      await this.sendNotification(invitedUserId, notificationData);

      // Send real-time notification
      sendToUser(invitedUserId, 'group_invite', {
        groupId,
        groupName,
        inviterName,
        timestamp: new Date(),
      });

      logger.info(`Group invite notification sent to user ${invitedUserId}`);
    } catch (error) {
      logger.error('Error sending group invite notification:', error);
      throw error;
    }
  }

  // Send notification when new channel is created
  static async notifyChannelCreated(
    groupId: number,
    channelName: string,
    creatorName: string
  ) {
    try {
      // Get group members
      const group = await prisma.forumGroup.findUnique({
        where: { id: groupId },
        include: {
          members: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      if (!group) {
        throw new Error('Group not found');
      }

      // Notify all group members
      for (const member of group.members) {
        const notificationData: NotificationData = {
          type: 'forum_channel_created',
          title: `New channel created: ${channelName}`,
          message: `${creatorName} created a new channel "${channelName}" in ${group.name}`,
          data: {
            groupId,
            channelName,
            creatorName,
            groupName: group.name,
          },
          priority: 'low',
        };

        await this.sendNotification(member.staffId, notificationData);
      }

      // Send real-time notification to group
      sendToGroup(groupId, 'channel_created', {
        channelName,
        creatorName,
        groupId,
        groupName: group.name,
        timestamp: new Date(),
      });

      logger.info(`Channel creation notifications sent for ${channelName}`);
    } catch (error) {
      logger.error('Error sending channel creation notifications:', error);
      throw error;
    }
  }

  // Send notification when member joins group
  static async notifyMemberJoined(
    groupId: number,
    newMemberName: string,
    newMemberId: number
  ) {
    try {
      const group = await prisma.forumGroup.findUnique({
        where: { id: groupId },
        include: {
          members: {
            where: {
              staffId: { not: newMemberId },
            },
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      if (!group) {
        throw new Error('Group not found');
      }

      // Notify existing members
      for (const member of group.members) {
        const notificationData: NotificationData = {
          type: 'forum_member_joined',
          title: `New member joined ${group.name}`,
          message: `${newMemberName} joined the group`,
          data: {
            groupId,
            groupName: group.name,
            newMemberName,
            newMemberId,
          },
          priority: 'low',
        };

        await this.sendNotification(member.staffId, notificationData);
      }

      // Send real-time notification to group
      sendToGroup(groupId, 'member_joined', {
        groupId,
        groupName: group.name,
        newMemberName,
        newMemberId,
        timestamp: new Date(),
      });

      logger.info(`Member joined notifications sent for ${newMemberName}`);
    } catch (error) {
      logger.error('Error sending member joined notifications:', error);
      throw error;
    }
  }

  // Core notification sending method
  private static async sendNotification(
    userId: number,
    notificationData: NotificationData
  ) {
    try {
      // Send via existing messaging service
      await messagingService.sendNotification(
        userId,
        notificationData.message,
        this.getNotificationType(notificationData.priority || 'low')
      );

      // Store notification in database for persistence
      await prisma.notification.create({
        data: {
          staffId: userId,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          data: notificationData.data
            ? JSON.stringify(notificationData.data)
            : null,
          priority: notificationData.priority || 'low',
          isRead: false,
        },
      });
    } catch (error) {
      logger.error('Error sending notification:', error);
      throw error;
    }
  }

  // Send email notification for mentions
  private static async sendMentionEmail(
    userId: number,
    authorName: string,
    channelName: string,
    groupName: string,
    content: string
  ) {
    try {
      const user = await prisma.staff.findUnique({
        where: { id: userId },
        select: {
          fullName: true,
          email: true,
        },
      });

      if (!user || !user.email) {
        logger.warn(
          `User ${userId} not found or has no email for mention notification`
        );
        return;
      }

      const mailOptions = {
        from: '"Cedarcrest Hospitals Forum" <<EMAIL>>',
        to: user.email,
        subject: `You were mentioned in ${groupName} > ${channelName}`,
        template: 'forum-mention',
        context: {
          userName: `${user.fullName}`,
          authorName,
          groupName,
          channelName,
          content: content.substring(0, 300),
          dashboardUrl: `${config.ADMIN_DASHBOARD_BASE_URL}/forum`,
        },
      };

      await enqueueSendEmailJob(mailOptions);
      logger.info(`Mention email queued for user ${userId}`);
    } catch (error) {
      logger.error('Error sending mention email:', error);
    }
  }

  // Helper method to convert priority to notification type
  private static getNotificationType(
    priority: string
  ): 'info' | 'success' | 'warning' | 'error' {
    switch (priority) {
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
      default:
        return 'info';
    }
  }
}
