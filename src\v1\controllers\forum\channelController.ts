import { Request, Response } from 'express';
import { ForumChannelService } from '../../services/forum/channelService';
import { logger } from '../../utils/logger';

export class ForumChannelController {
  // Create a new channel
  static async createChannel(req: Request, res: Response) {
    try {
      const { name, description, groupId, isPrivate } = req.body;
      const createdById = req.Staff as unknown as number;

      if (!createdById) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      if (!name || !groupId) {
        res.status(400).json({
          success: false,
          message: 'Channel name and group ID are required',
        });
        return;
      }

      const channel = await ForumChannelService.createChannel({
        name,
        description,
        groupId: parseInt(groupId),
        isPrivate: isPrivate || false,
        createdById,
      });

      res.status(201).json({
        success: true,
        message: 'Channel created successfully',
        data: channel,
      });
    } catch (error) {
      logger.error('Error creating channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to create channel',
      });
    }
  }

  // Get channel by ID
  static async getChannel(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const channel = await ForumChannelService.getChannelById(
        parseInt(channelId),
        userId
      );

      res.status(200).json({
        success: true,
        data: channel,
      });
    } catch (error) {
      logger.error('Error getting channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to get channel',
      });
    }
  }

  // Get channels in a group
  static async getGroupChannels(req: Request, res: Response) {
    try {
      const { groupId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const channels = await ForumChannelService.getGroupChannels(
        parseInt(groupId),
        userId
      );

      res.status(200).json({
        success: true,
        data: channels,
      });
    } catch (error) {
      logger.error('Error getting group channels:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get group channels',
      });
    }
  }

  // Update channel
  static async updateChannel(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const { name, description, isPrivate } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const channel = await ForumChannelService.updateChannel(
        parseInt(channelId),
        { name, description, isPrivate },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Channel updated successfully',
        data: channel,
      });
    } catch (error) {
      logger.error('Error updating channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to update channel',
      });
    }
  }

  // Delete channel
  static async deleteChannel(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await ForumChannelService.deleteChannel(parseInt(channelId), userId);

      res.status(200).json({
        success: true,
        message: 'Channel deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to delete channel',
      });
    }
  }

  // Pin message in channel
  static async pinMessage(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const { messageId } = req.body;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      if (!messageId) {
        res.status(400).json({
          success: false,
          message: 'Message ID is required',
        });
      }

      const pinnedMessage = await ForumChannelService.pinMessage(
        parseInt(channelId),
        messageId,
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Message pinned successfully',
        data: pinnedMessage,
      });
    } catch (error) {
      logger.error('Error pinning message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to pin message',
      });
    }
  }

  // Unpin message in channel
  static async unpinMessage(req: Request, res: Response) {
    try {
      const { channelId, messageId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      await ForumChannelService.unpinMessage(
        parseInt(channelId),
        messageId,
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Message unpinned successfully',
      });
    } catch (error) {
      logger.error('Error unpinning message:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to unpin message',
      });
    }
  }

  // Get pinned messages in channel
  static async getPinnedMessages(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const pinnedMessages = await ForumChannelService.getPinnedMessages(
        parseInt(channelId),
        userId
      );

      res.status(200).json({
        success: true,
        data: pinnedMessages,
      });
    } catch (error) {
      logger.error('Error getting pinned messages:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get pinned messages',
      });
    }
  }

  // Get channel statistics
  static async getChannelStats(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      // First verify user has access to channel
      await ForumChannelService.getChannelById(parseInt(channelId), userId);

      const stats = await ForumChannelService.getChannelStats(
        parseInt(channelId)
      );

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting channel stats:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to get channel statistics',
      });
    }
  }

  // Archive channel
  static async archiveChannel(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const channel = await ForumChannelService.updateChannel(
        parseInt(channelId),
        { isActive: false },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Channel archived successfully',
        data: channel,
      });
    } catch (error) {
      logger.error('Error archiving channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to archive channel',
      });
    }
  }

  // Restore archived channel
  static async restoreChannel(req: Request, res: Response) {
    try {
      const { channelId } = req.params;
      const userId = req.Staff as unknown as number;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
      }

      const channel = await ForumChannelService.updateChannel(
        parseInt(channelId),
        { isActive: true },
        userId
      );

      res.status(200).json({
        success: true,
        message: 'Channel restored successfully',
        data: channel,
      });
    } catch (error) {
      logger.error('Error restoring channel:', error);
      res.status(500).json({
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to restore channel',
      });
    }
  }

  // Unarchive channel (alias for restore)
  static async unarchiveChannel(req: Request, res: Response) {
    return ForumChannelController.restoreChannel(req, res);
  }
}
