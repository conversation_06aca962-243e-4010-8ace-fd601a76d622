import { Router } from 'express';
import { NotificationController } from '../../controllers/notificationController';
import { secure } from '../../middleware/auth';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';

const router = Router();

// Apply authentication middleware to all routes
router.use(secure);

// Get user notifications with pagination and filters
// GET /notifications?page=1&limit=20&type=forum_message&priority=high&isRead=false&startDate=2024-01-01&endDate=2024-12-31
router.get('/', NotificationController.getNotifications);

// Get unread notification count
// GET /notifications/unread-count
router.get('/unread-count', NotificationController.getUnreadCount);

// Get notification statistics
// GET /notifications/stats
router.get('/stats', NotificationController.getStats);

// Get recent activity for dashboard
// GET /notifications/recent?limit=10
router.get('/recent', NotificationController.getRecentActivity);

// Get notification preferences
// GET /notifications/preferences
router.get('/preferences', NotificationController.getPreferences);

// Update notification preferences
// PUT /notifications/preferences
router.put('/preferences', NotificationController.updatePreferences);

// Mark specific notifications as read
// PUT /notifications/mark-read
router.put('/mark-read', NotificationController.markAsRead);

// Mark all notifications as read
// PUT /notifications/mark-all-read
router.put('/mark-all-read', NotificationController.markAllAsRead);

// Delete specific notifications
// DELETE /notifications
router.delete('/', NotificationController.deleteNotifications);

// Admin routes - require admin permissions
// Send bulk notification to multiple users
// POST /notifications/bulk
router.post(
  '/bulk',
  async (req: any, res, next) => {
    try {
      const hasPermission = await staffHasPermission(
        req.Staff,
        PERMISSIONS.STAFF_EDIT
      );
      next();
    } catch (error) {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions to send bulk notifications',
      });
    }
  },
  NotificationController.sendBulkNotification
);

// Clean up old notifications
// DELETE /notifications/cleanup
router.delete(
  '/cleanup',
  async (req: any, res, next) => {
    try {
      const hasPermission = await staffHasPermission(
        req.Staff,
        PERMISSIONS.STAFF_EDIT
      );
      next();
    } catch (error) {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions to cleanup notifications',
      });
    }
  },
  NotificationController.cleanupOldNotifications
);

export default router;
