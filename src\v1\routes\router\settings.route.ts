import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { settingsControllers } from '../../controllers/settings/settings.controller';

export const settingsRoute = Router();

// General settings routes
settingsRoute.get('/list', secure, settingsControllers.ListSettingsHandler);
settingsRoute.post('/create', secure, settingsControllers.CreateSettingHandler);
settingsRoute.patch(
  '/update',
  secure,
  settingsControllers.UpdateSettingHandler
);
settingsRoute.delete(
  '/delete/:id',
  secure,
  settingsControllers.DeleteSettingHandler
);

// Admin notification emails routes
settingsRoute.get(
  '/admin-notification-emails',
  secure,
  settingsControllers.GetAdminNotificationEmailsHandler
);
settingsRoute.post(
  '/admin-notification-emails',
  secure,
  settingsControllers.UpdateAdminNotificationEmailsHandler
);
