import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';

const prisma = new PrismaClient();

export interface GameAnalyticsFilters {
  startDate?: Date;
  endDate?: Date;
  locationId?: number;
  gameType?: string;
  createdById?: number;
}

export class GameAnalyticsService {
  // Get overall game statistics
  static async getGameStatistics(filters: GameAnalyticsFilters = {}) {
    try {
      const where: any = {};

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) where.createdAt.gte = filters.startDate;
        if (filters.endDate) where.createdAt.lte = filters.endDate;
      }

      if (filters.locationId) where.locationId = filters.locationId;
      if (filters.gameType) where.gameType = filters.gameType;
      if (filters.createdById) where.createdById = filters.createdById;

      const [
        totalGames,
        activeGames,
        completedGames,
        totalParticipants,
        totalRewardsDistributed,
        gamesByType,
        gamesByStatus,
      ] = await Promise.all([
        // Total games
        prisma.game.count({ where }),

        // Active games
        prisma.game.count({
          where: { ...where, status: 'ACTIVE' },
        }),

        // Completed games
        prisma.game.count({
          where: { ...where, status: 'COMPLETED' },
        }),

        // Total participants across all games
        prisma.gameParticipant.count({
          where: {
            game: where,
          },
        }),

        // Total rewards distributed
        prisma.gameReward.aggregate({
          where: {
            isAwarded: true,
            game: where,
          },
          _sum: {
            amount: true,
          },
        }),

        // Games by type
        prisma.game.groupBy({
          by: ['gameType'],
          where,
          _count: {
            id: true,
          },
        }),

        // Games by status
        prisma.game.groupBy({
          by: ['status'],
          where,
          _count: {
            id: true,
          },
        }),
      ]);

      return {
        totalGames,
        activeGames,
        completedGames,
        draftGames: totalGames - activeGames - completedGames,
        totalParticipants,
        averageParticipantsPerGame:
          totalGames > 0 ? Math.round(totalParticipants / totalGames) : 0,
        totalRewardsDistributed: totalRewardsDistributed._sum.amount || 0,
        gamesByType: gamesByType.map((item) => ({
          type: item.gameType,
          count: item._count.id,
        })),
        gamesByStatus: gamesByStatus.map((item) => ({
          status: item.status,
          count: item._count.id,
        })),
      };
    } catch (error) {
      logger.error('Error getting game statistics:', error);
      throw error;
    }
  }

  // Get participation analytics
  static async getParticipationAnalytics(filters: GameAnalyticsFilters = {}) {
    try {
      const where: any = {};

      if (filters.startDate || filters.endDate) {
        where.joinedAt = {};
        if (filters.startDate) where.joinedAt.gte = filters.startDate;
        if (filters.endDate) where.joinedAt.lte = filters.endDate;
      }

      if (filters.locationId) {
        where.game = { locationId: filters.locationId };
      }

      const [
        totalParticipations,
        completedParticipations,
        withdrawnParticipations,
        participationsByStatus,
        topParticipants,
        participationTrends,
      ] = await Promise.all([
        // Total participations
        prisma.gameParticipant.count({ where }),

        // Completed participations
        prisma.gameParticipant.count({
          where: { ...where, status: 'COMPLETED' },
        }),

        // Withdrawn participations
        prisma.gameParticipant.count({
          where: { ...where, status: 'WITHDRAWN' },
        }),

        // Participations by status
        prisma.gameParticipant.groupBy({
          by: ['status'],
          where,
          _count: {
            id: true,
          },
        }),

        // Top participants by number of games
        prisma.gameParticipant.groupBy({
          by: ['staffId'],
          where,
          _count: {
            id: true,
          },
          orderBy: {
            _count: {
              id: 'desc',
            },
          },
          take: 10,
        }),

        // Participation trends by month
        prisma.$queryRaw`
          SELECT 
            DATE_TRUNC('month', joined_at) as month,
            COUNT(*) as count
          FROM game_participants 
          WHERE joined_at >= COALESCE(${filters.startDate}, NOW() - INTERVAL '12 months')
            AND joined_at <= COALESCE(${filters.endDate}, NOW())
          GROUP BY DATE_TRUNC('month', joined_at)
          ORDER BY month DESC
          LIMIT 12
        `,
      ]);

      // Get staff details for top participants
      const topParticipantIds = topParticipants.map((p) => p.staffId);
      const staffDetails = await prisma.staff.findMany({
        where: { id: { in: topParticipantIds } },
        select: {
          id: true,
          fullName: true,
        },
      });

      const topParticipantsWithDetails = topParticipants.map((p) => ({
        staff: staffDetails.find((s) => s.id === p.staffId),
        participationCount: p._count.id,
      }));

      return {
        totalParticipations,
        completedParticipations,
        withdrawnParticipations,
        activeParticipations:
          totalParticipations -
          completedParticipations -
          withdrawnParticipations,
        completionRate:
          totalParticipations > 0
            ? (completedParticipations / totalParticipations) * 100
            : 0,
        withdrawalRate:
          totalParticipations > 0
            ? (withdrawnParticipations / totalParticipations) * 100
            : 0,
        participationsByStatus: participationsByStatus.map((item) => ({
          status: item.status,
          count: item._count.id,
        })),
        topParticipants: topParticipantsWithDetails,
        participationTrends,
      };
    } catch (error) {
      logger.error('Error getting participation analytics:', error);
      throw error;
    }
  }

  // Get reward analytics
  static async getRewardAnalytics(filters: GameAnalyticsFilters = {}) {
    try {
      const where: any = {};

      if (filters.startDate || filters.endDate) {
        where.awardedAt = {};
        if (filters.startDate) where.awardedAt.gte = filters.startDate;
        if (filters.endDate) where.awardedAt.lte = filters.endDate;
      }

      if (filters.locationId) {
        where.game = { locationId: filters.locationId };
      }

      const [
        totalRewards,
        awardedRewards,
        totalRewardAmount,
        rewardsByType,
        topWinners,
        rewardTrends,
      ] = await Promise.all([
        // Total rewards configured
        prisma.gameReward.count({
          where: {
            game: filters.locationId
              ? { locationId: filters.locationId }
              : undefined,
          },
        }),

        // Awarded rewards
        prisma.gameReward.count({
          where: { ...where, isAwarded: true },
        }),

        // Total reward amount distributed
        prisma.gameReward.aggregate({
          where: { ...where, isAwarded: true },
          _sum: {
            amount: true,
          },
        }),

        // Rewards by type
        prisma.gameReward.groupBy({
          by: ['rewardType'],
          where: { ...where, isAwarded: true },
          _count: {
            id: true,
          },
          _sum: {
            amount: true,
          },
        }),

        // Top winners by reward amount
        prisma.gameReward.groupBy({
          by: ['awardedToStaffId'],
          where: { ...where, isAwarded: true, awardedToStaffId: { not: null } },
          _sum: {
            amount: true,
          },
          _count: {
            id: true,
          },
          orderBy: {
            _sum: {
              amount: 'desc',
            },
          },
          take: 10,
        }),

        // Reward trends by month
        prisma.$queryRaw`
          SELECT 
            DATE_TRUNC('month', awarded_at) as month,
            COUNT(*) as count,
            SUM(amount) as total_amount
          FROM game_rewards 
          WHERE is_awarded = true
            AND awarded_at >= COALESCE(${filters.startDate}, NOW() - INTERVAL '12 months')
            AND awarded_at <= COALESCE(${filters.endDate}, NOW())
          GROUP BY DATE_TRUNC('month', awarded_at)
          ORDER BY month DESC
          LIMIT 12
        `,
      ]);

      // Get staff details for top winners
      const topWinnerIds = topWinners
        .map((w) => w.awardedToStaffId)
        .filter((id) => id !== null) as number[];

      const staffDetails = await prisma.staff.findMany({
        where: { id: { in: topWinnerIds } },
        select: {
          id: true,
          fullName: true,
        },
      });

      const topWinnersWithDetails = topWinners.map((w) => ({
        staff: staffDetails.find((s) => s.id === w.awardedToStaffId),
        totalAmount: w._sum.amount || 0,
        rewardCount: w._count.id,
      }));

      return {
        totalRewards,
        awardedRewards,
        pendingRewards: totalRewards - awardedRewards,
        totalRewardAmount: Number(totalRewardAmount._sum.amount || 0),
        averageRewardAmount:
          awardedRewards > 0
            ? Number(totalRewardAmount._sum.amount || 0) / awardedRewards
            : 0,
        rewardsByType: rewardsByType.map((item) => ({
          type: item.rewardType,
          count: item._count.id,
          totalAmount: Number(item._sum.amount || 0),
        })),
        topWinners: topWinnersWithDetails,
        rewardTrends,
      };
    } catch (error) {
      logger.error('Error getting reward analytics:', error);
      throw error;
    }
  }

  // Get game performance analytics
  static async getGamePerformanceAnalytics(gameId: string) {
    try {
      const [game, participantStats, questionStats, completionStats] =
        await Promise.all([
          // Game details
          prisma.game.findUnique({
            where: { id: gameId },
            include: {
              _count: {
                select: {
                  participants: true,
                  questions: true,
                },
              },
            },
          }),

          // Participant statistics
          prisma.gameParticipant.groupBy({
            by: ['status'],
            where: { gameId },
            _count: {
              id: true,
            },
            _avg: {
              score: true,
            },
          }),

          // Question statistics
          prisma.gameAnswer.groupBy({
            by: ['questionId'],
            where: {
              participant: {
                gameId,
              },
              isCorrect: true,
            },
            _count: {
              id: true,
            },
            _avg: {
              timeSpent: true,
            },
          }),

          // Completion time statistics
          prisma.gameParticipant.aggregate({
            where: {
              gameId,
              status: 'COMPLETED',
              completedAt: { not: null },
            },
            _avg: {
              score: true,
            },
            _min: {
              completedAt: true,
            },
            _max: {
              completedAt: true,
            },
          }),
        ]);

      if (!game) {
        throw new Error('Game not found');
      }

      return {
        game: {
          id: game.id,
          title: game.title,
          status: game.status,
          totalParticipants: game._count.participants,
          totalQuestions: game._count.questions,
        },
        participantStats: participantStats.map((stat) => ({
          status: stat.status,
          count: stat._count.id,
          averageScore: stat._avg.score || 0,
        })),
        questionStats,
        completionStats: {
          averageScore: completionStats._avg.score || 0,
          fastestCompletion: completionStats._min.completedAt,
          slowestCompletion: completionStats._max.completedAt,
        },
      };
    } catch (error) {
      logger.error('Error getting game performance analytics:', error);
      throw error;
    }
  }
}
