import { Server as SocketIOServer, Socket } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { redis } from '../utils/cache';
import { logger } from '../utils/logger';
import jwt from 'jsonwebtoken';
import config from '../../config/app.config';

interface AuthenticatedSocket extends Socket {
  userId: number;
}

interface TypingData {
  userId: number;
  userName: string;
  channelId?: number;
  receiverId?: number;
}

interface OnlineUser {
  userId: number;
  userName: string;
  lastSeen: Date;
  status: 'online' | 'away' | 'busy' | 'offline';
}

export let io: SocketIOServer;

// Store online users and typing indicators
const onlineUsers = new Map<number, OnlineUser>();
const typingUsers = new Map<string, TypingData>();

export const initializeSocket = (server: any) => {
  io = new SocketIOServer(server, {
    cors: {
      origin: config.CORS_ORIGINS.split(','),
      methods: ['GET', 'POST'],
    },
  });

  // Redis adapter for scaling
  const pubClient = redis;
  const subClient = redis.duplicate();
  io.adapter(createAdapter(pubClient, subClient));

  // Authentication middleware
  io.use((socket, next) => {
    const token = socket.handshake.auth.token;
    if (!token) return next(new Error('Authentication error'));

    try {
      const decoded = jwt.verify(token, config.SIGNING_TOKEN_SECRET) as any;
      socket.userId = decoded.id;
      next();
    } catch (err) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket: Socket) => {
    const authenticatedSocket = socket as AuthenticatedSocket;
    logger.info(`User ${authenticatedSocket.userId} connected`);

    // Join user-specific room
    authenticatedSocket.join(`user:${authenticatedSocket.userId}`);

    // Handle user coming online
    handleUserOnline(authenticatedSocket);

    // Forum-related events
    setupForumEvents(authenticatedSocket);

    // Game-related events
    setupGameEvents(authenticatedSocket);

    // Direct messaging events
    setupDirectMessageEvents(authenticatedSocket);

    // General events
    setupGeneralEvents(authenticatedSocket);

    authenticatedSocket.on('disconnect', () => {
      logger.info(`User ${authenticatedSocket.userId} disconnected`);
      handleUserOffline(authenticatedSocket);
    });
  });

  logger.info('Socket.IO initialized with Redis adapter');
};

// User presence management
const handleUserOnline = async (socket: AuthenticatedSocket) => {
  try {
    // Get user info from database (you'll need to implement this)
    const userInfo = {
      userId: socket.userId,
      userName: 'User Name', // Get from database
      lastSeen: new Date(),
      status: 'online' as const,
    };

    onlineUsers.set(socket.userId, userInfo);

    // Store online status in Redis
    await redis.hset(
      'online_users',
      socket.userId.toString(),
      JSON.stringify(userInfo)
    );
    await redis.expire('online_users', 86400); // 24 hours

    // Broadcast user online status to relevant groups/channels
    socket.broadcast.emit('user_online', {
      userId: socket.userId,
      status: 'online',
      lastSeen: new Date(),
    });

    logger.info(`User ${socket.userId} is now online`);
  } catch (error) {
    logger.error('Error handling user online:', error);
  }
};

const handleUserOffline = async (socket: AuthenticatedSocket) => {
  try {
    onlineUsers.delete(socket.userId);

    // Update Redis
    await redis.hdel('online_users', socket.userId.toString());

    // Clear typing indicators
    const typingKeys = Array.from(typingUsers.keys()).filter(
      (key) => typingUsers.get(key)?.userId === socket.userId
    );
    typingKeys.forEach((key) => typingUsers.delete(key));

    // Broadcast user offline status
    socket.broadcast.emit('user_offline', {
      userId: socket.userId,
      status: 'offline',
      lastSeen: new Date(),
    });

    logger.info(`User ${socket.userId} is now offline`);
  } catch (error) {
    logger.error('Error handling user offline:', error);
  }
};

// Forum-related event handlers
const setupForumEvents = (socket: AuthenticatedSocket) => {
  // Join group channels
  socket.on('join_group', (data: { groupId: number }) => {
    socket.join(`group:${data.groupId}`);
    socket.to(`group:${data.groupId}`).emit('user_joined_group', {
      userId: socket.userId,
      groupId: data.groupId,
    });
    logger.info(`User ${socket.userId} joined group ${data.groupId}`);
  });

  // Leave group channels
  socket.on('leave_group', (data: { groupId: number }) => {
    socket.leave(`group:${data.groupId}`);
    socket.to(`group:${data.groupId}`).emit('user_left_group', {
      userId: socket.userId,
      groupId: data.groupId,
    });
    logger.info(`User ${socket.userId} left group ${data.groupId}`);
  });

  // Join specific channel
  socket.on('join_channel', (data: { channelId: number }) => {
    socket.join(`channel:${data.channelId}`);
    logger.info(`User ${socket.userId} joined channel ${data.channelId}`);
  });

  // Leave specific channel
  socket.on('leave_channel', (data: { channelId: number }) => {
    socket.leave(`channel:${data.channelId}`);
    logger.info(`User ${socket.userId} left channel ${data.channelId}`);
  });

  // Typing indicators for channels
  socket.on('typing_start', (data: { channelId: number; userName: string }) => {
    const typingKey = `channel:${data.channelId}:${socket.userId}`;
    typingUsers.set(typingKey, {
      userId: socket.userId,
      userName: data.userName,
      channelId: data.channelId,
    });

    socket.to(`channel:${data.channelId}`).emit('user_typing', {
      userId: socket.userId,
      userName: data.userName,
      channelId: data.channelId,
    });

    // Auto-clear typing after 3 seconds
    setTimeout(() => {
      if (typingUsers.has(typingKey)) {
        typingUsers.delete(typingKey);
        socket.to(`channel:${data.channelId}`).emit('user_stopped_typing', {
          userId: socket.userId,
          channelId: data.channelId,
        });
      }
    }, 3000);
  });

  socket.on('typing_stop', (data: { channelId: number }) => {
    const typingKey = `channel:${data.channelId}:${socket.userId}`;
    typingUsers.delete(typingKey);
    socket.to(`channel:${data.channelId}`).emit('user_stopped_typing', {
      userId: socket.userId,
      channelId: data.channelId,
    });
  });

  // New message in channel
  socket.on('new_message', (data: { channelId: number; message: any }) => {
    socket.to(`channel:${data.channelId}`).emit('message_received', {
      channelId: data.channelId,
      message: data.message,
    });
  });

  // Message reactions
  socket.on(
    'message_reaction',
    (data: { messageId: string; emoji: string; channelId: number }) => {
      socket.to(`channel:${data.channelId}`).emit('reaction_added', {
        messageId: data.messageId,
        emoji: data.emoji,
        userId: socket.userId,
      });
    }
  );

  // Message mentions
  socket.on(
    'user_mentioned',
    (data: {
      mentionedUserId: number;
      messageId: string;
      channelId: number;
    }) => {
      socket.to(`user:${data.mentionedUserId}`).emit('mentioned_in_message', {
        messageId: data.messageId,
        channelId: data.channelId,
        mentionedBy: socket.userId,
      });
    }
  );
};

// Direct messaging event handlers
const setupDirectMessageEvents = (socket: AuthenticatedSocket) => {
  // Typing indicators for direct messages
  socket.on(
    'dm_typing_start',
    (data: { receiverId: number; userName: string }) => {
      const typingKey = `dm:${socket.userId}:${data.receiverId}`;
      typingUsers.set(typingKey, {
        userId: socket.userId,
        userName: data.userName,
        receiverId: data.receiverId,
      });

      socket.to(`user:${data.receiverId}`).emit('dm_user_typing', {
        userId: socket.userId,
        userName: data.userName,
      });

      // Auto-clear typing after 3 seconds
      setTimeout(() => {
        if (typingUsers.has(typingKey)) {
          typingUsers.delete(typingKey);
          socket.to(`user:${data.receiverId}`).emit('dm_user_stopped_typing', {
            userId: socket.userId,
          });
        }
      }, 3000);
    }
  );

  socket.on('dm_typing_stop', (data: { receiverId: number }) => {
    const typingKey = `dm:${socket.userId}:${data.receiverId}`;
    typingUsers.delete(typingKey);
    socket.to(`user:${data.receiverId}`).emit('dm_user_stopped_typing', {
      userId: socket.userId,
    });
  });

  // New direct message
  socket.on(
    'new_direct_message',
    (data: { receiverId: number; message: any }) => {
      socket.to(`user:${data.receiverId}`).emit('direct_message_received', {
        senderId: socket.userId,
        message: data.message,
      });
    }
  );

  // Mark message as read
  socket.on('mark_dm_read', (data: { senderId: number; messageId: string }) => {
    socket.to(`user:${data.senderId}`).emit('dm_message_read', {
      messageId: data.messageId,
      readBy: socket.userId,
      readAt: new Date(),
    });
  });
};

// Game-related event handlers
const setupGameEvents = (socket: AuthenticatedSocket) => {
  // Join game room
  socket.on('join_game', (data: { gameId: string }) => {
    socket.join(`game:${data.gameId}`);
    socket.to(`game:${data.gameId}`).emit('player_joined', {
      userId: socket.userId,
      gameId: data.gameId,
    });
    logger.info(`User ${socket.userId} joined game ${data.gameId}`);
  });

  // Leave game room
  socket.on('leave_game', (data: { gameId: string }) => {
    socket.leave(`game:${data.gameId}`);
    socket.to(`game:${data.gameId}`).emit('player_left', {
      userId: socket.userId,
      gameId: data.gameId,
    });
    logger.info(`User ${socket.userId} left game ${data.gameId}`);
  });

  // Game started
  socket.on('game_started', (data: { gameId: string }) => {
    socket.to(`game:${data.gameId}`).emit('game_started', {
      gameId: data.gameId,
      startedBy: socket.userId,
      startTime: new Date(),
    });
  });

  // Player answered question
  socket.on(
    'player_answered',
    (data: { gameId: string; questionId: string; answer: any }) => {
      socket.to(`game:${data.gameId}`).emit('player_answered', {
        gameId: data.gameId,
        questionId: data.questionId,
        playerId: socket.userId,
        timestamp: new Date(),
      });
    }
  );

  // Leaderboard update
  socket.on(
    'leaderboard_update',
    (data: { gameId: string; leaderboard: any[] }) => {
      socket.to(`game:${data.gameId}`).emit('leaderboard_updated', {
        gameId: data.gameId,
        leaderboard: data.leaderboard,
        updatedAt: new Date(),
      });
    }
  );

  // Game completed
  socket.on('game_completed', (data: { gameId: string; results: any }) => {
    socket.to(`game:${data.gameId}`).emit('game_completed', {
      gameId: data.gameId,
      results: data.results,
      completedAt: new Date(),
    });
  });

  // Reward distributed
  socket.on(
    'reward_distributed',
    (data: { gameId: string; rewards: any[] }) => {
      data.rewards.forEach((reward: any) => {
        socket.to(`user:${reward.userId}`).emit('reward_received', {
          gameId: data.gameId,
          reward: reward,
          receivedAt: new Date(),
        });
      });
    }
  );
};

// General event handlers
const setupGeneralEvents = (socket: AuthenticatedSocket) => {
  // Status update
  socket.on(
    'status_update',
    async (data: { status: 'online' | 'away' | 'busy' }) => {
      const user = onlineUsers.get(socket.userId);
      if (user) {
        user.status = data.status;
        onlineUsers.set(socket.userId, user);

        // Update Redis
        await redis.hset(
          'online_users',
          socket.userId.toString(),
          JSON.stringify(user)
        );

        // Broadcast status update
        socket.broadcast.emit('user_status_changed', {
          userId: socket.userId,
          status: data.status,
          updatedAt: new Date(),
        });
      }
    }
  );

  // Get online users
  socket.on('get_online_users', (callback) => {
    const users = Array.from(onlineUsers.values());
    callback(users);
  });

  // Ping/pong for connection health
  socket.on('ping', (callback) => {
    callback('pong');
  });
};

// Utility functions for external use
export const sendToUser = (userId: number, event: string, data: any) => {
  io.to(`user:${userId}`).emit(event, data);
};

export const sendToChannel = (channelId: number, event: string, data: any) => {
  io.to(`channel:${channelId}`).emit(event, data);
};

export const sendToGroup = (groupId: number, event: string, data: any) => {
  io.to(`group:${groupId}`).emit(event, data);
};

export const sendToGame = (gameId: string, event: string, data: any) => {
  io.to(`game:${gameId}`).emit(event, data);
};

export const broadcast = (event: string, data: any) => {
  io.emit(event, data);
};

// Notification functions
export const notifyNewMessage = (channelId: number, message: any) => {
  sendToChannel(channelId, 'new_message', {
    channelId,
    message,
    timestamp: new Date(),
  });
};

export const notifyDirectMessage = (
  receiverId: number,
  senderId: number,
  message: any
) => {
  sendToUser(receiverId, 'new_direct_message', {
    senderId,
    message,
    timestamp: new Date(),
  });
};

export const notifyGameUpdate = (
  gameId: string,
  updateType: string,
  data: any
) => {
  sendToGame(gameId, 'game_update', {
    gameId,
    updateType,
    data,
    timestamp: new Date(),
  });
};

export const notifyUserMention = (userId: number, mentionData: any) => {
  sendToUser(userId, 'user_mentioned', {
    ...mentionData,
    timestamp: new Date(),
  });
};

export const notifyRewardReceived = (userId: number, reward: any) => {
  sendToUser(userId, 'reward_received', {
    reward,
    timestamp: new Date(),
  });
};

// Get online users count
export const getOnlineUsersCount = (): number => {
  return onlineUsers.size;
};

// Get online users in a specific group/channel
export const getOnlineUsersInRoom = async (
  roomName: string
): Promise<string[]> => {
  const sockets = await io.in(roomName).fetchSockets();
  return sockets
    .map((socket) => (socket as any).userId?.toString())
    .filter(Boolean);
};

// Check if user is online
export const isUserOnline = (userId: number): boolean => {
  return onlineUsers.has(userId);
};

// Get user status
export const getUserStatus = (userId: number): OnlineUser | null => {
  return onlineUsers.get(userId) || null;
};
