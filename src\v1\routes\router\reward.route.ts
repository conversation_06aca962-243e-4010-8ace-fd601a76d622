import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { rewardControllers } from '../../controllers/reward/reward.controller';

export const rewardRoute = Router();

rewardRoute.get('/list', secure, rewardControllers.ListRewardHandler);

rewardRoute.patch('/update', secure, rewardControllers.UpdateRewardHandler);

rewardRoute.post('/create', secure, rewardControllers.CreateRewardHandler);

rewardRoute.post(
  '/referral/validate',
  rewardControllers.VerifyReferralCodeHandler
);
