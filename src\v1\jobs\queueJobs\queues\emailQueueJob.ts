import { getQueue } from '../../../utils/queue';
import { logger, devLog } from '../../../utils/logger';

/**
 * Add an email job to the queue
 * @param mailOptions Email options to be passed to the nodemailer
 * @returns Promise that resolves with the job ID
 */
export const enqueueSendEmailJob = async (
  mailOptions: any
): Promise<string> => {
  try {
    const emailQueue = getQueue('send-email');
    const job = await emailQueue.add({ mailOptions });
    logger.info(`Send email job created with ID ${job.id}`);
    return job.id.toString();
  } catch (error) {
    logger.error('Error adding email job to queue:', error);
    throw error;
  }
};
