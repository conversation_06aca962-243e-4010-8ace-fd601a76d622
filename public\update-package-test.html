<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Update Package Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1,
      h2 {
        color: #333;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input,
      select,
      textarea {
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
      }
      button:hover {
        background-color: #45a049;
      }
      .tab {
        overflow: hidden;
        border: 1px solid #ccc;
        background-color: #f1f1f1;
        margin-bottom: 20px;
      }
      .tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 14px 16px;
        transition: 0.3s;
        color: black;
      }
      .tab button:hover {
        background-color: #ddd;
      }
      .tab button.active {
        background-color: #ccc;
      }
      .tabcontent {
        display: none;
        padding: 20px;
        border: 1px solid #ccc;
        border-top: none;
      }
      #result {
        margin-top: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
        min-height: 100px;
      }
      .error {
        color: red;
      }
      .success {
        color: green;
      }
    </style>
  </head>
  <body>
    <h1>Update Package Test</h1>

    <div class="tab">
      <button class="tablinks active" onclick="openTab(event, 'Details')">
        Update Details
      </button>
      <button class="tablinks" onclick="openTab(event, 'Prices')">
        Update Prices
      </button>
      <button class="tablinks" onclick="openTab(event, 'Image')">
        Update Image
      </button>
    </div>

    <div id="Details" class="tabcontent" style="display: block">
      <h2>Update Package Details</h2>
      <form id="detailsForm">
        <div class="form-group">
          <label for="packageId">Package ID:</label>
          <input type="number" id="packageId" name="id" required />
        </div>

        <div class="form-group">
          <label for="name">Name:</label>
          <input type="text" id="name" name="name" />
        </div>

        <div class="form-group">
          <label for="totalSlot">Total Slot:</label>
          <input type="number" id="totalSlot" name="totalSlot" />
        </div>

        <div class="form-group">
          <label for="categoryId">Category ID:</label>
          <input type="number" id="categoryId" name="categoryId" />
        </div>

        <div class="form-group">
          <label for="testIds">Test IDs (JSON array):</label>
          <textarea
            id="testIds"
            name="testIds"
            rows="3"
            placeholder="[1, 2, 3]"
          ></textarea>
        </div>

        <div class="form-group">
          <label for="packageStatus">Package Status:</label>
          <select id="packageStatus" name="packageStatus">
            <option value="">Select Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>
        </div>

        <button type="button" id="updateDetailsBtn">Update Details</button>
      </form>
    </div>

    <div id="Prices" class="tabcontent">
      <h2>Update Package Prices</h2>
      <form id="pricesForm">
        <div class="form-group">
          <label for="pricePackageId">Package ID:</label>
          <input type="number" id="pricePackageId" name="id" required />
        </div>

        <div class="form-group">
          <label for="prices">Prices (JSON array):</label>
          <textarea
            id="prices"
            name="prices"
            rows="6"
            placeholder='[{"location":{"id":1},"amount":5000,"currency":"NGN","endDate":"2023-12-31"}]'
          ></textarea>
        </div>

        <div class="form-group">
          <label for="pricesToDelete"
            >Prices to Delete (JSON array of IDs):</label
          >
          <textarea
            id="pricesToDelete"
            name="pricesToDelete"
            rows="3"
            placeholder="[1, 2, 3]"
          ></textarea>
        </div>

        <div class="form-group">
          <label for="replaceAllPrices">Replace All Prices:</label>
          <select id="replaceAllPrices" name="replaceAllPrices">
            <option value="false">No</option>
            <option value="true">Yes</option>
          </select>
        </div>

        <button type="button" id="updatePricesBtn">Update Prices</button>
      </form>
    </div>

    <div id="Image" class="tabcontent">
      <h2>Update Package Image</h2>
      <form id="imageForm" enctype="multipart/form-data">
        <div class="form-group">
          <label for="imagePackageId">Package ID:</label>
          <input type="number" id="imagePackageId" name="id" required />
        </div>

        <div class="form-group">
          <label for="image">Image:</label>
          <input
            type="file"
            id="image"
            name="image"
            accept="image/*"
            required
          />
        </div>

        <button type="button" id="updateImageBtn">Update Image</button>
      </form>
    </div>

    <div id="result"></div>

    <script>
      function openTab(evt, tabName) {
        var i, tabcontent, tablinks;
        tabcontent = document.getElementsByClassName('tabcontent');
        for (i = 0; i < tabcontent.length; i++) {
          tabcontent[i].style.display = 'none';
        }
        tablinks = document.getElementsByClassName('tablinks');
        for (i = 0; i < tablinks.length; i++) {
          tablinks[i].className = tablinks[i].className.replace(' active', '');
        }
        document.getElementById(tabName).style.display = 'block';
        evt.currentTarget.className += ' active';
      }

      document
        .getElementById('updateDetailsBtn')
        .addEventListener('click', async function () {
          const form = document.getElementById('detailsForm');
          const formData = new FormData(form);
          const packageId = formData.get('id');
          const resultDiv = document.getElementById('result');

          if (!packageId) {
            resultDiv.innerHTML =
              '<p class="error">Please enter Package ID</p>';
            return;
          }

          try {
            resultDiv.innerHTML = '<p>Sending request...</p>';

            // Parse testIds as JSON if provided
            if (formData.get('testIds')) {
              try {
                const testIds = JSON.parse(formData.get('testIds'));
                formData.set('testIds', JSON.stringify(testIds));
              } catch (e) {
                resultDiv.innerHTML =
                  '<p class="error">Invalid JSON format for Test IDs</p>';
                return;
              }
            }

            const response = await fetch(
              `/api/v1/package/update-package-details/${packageId}`,
              {
                method: 'PATCH',
                body: formData,
              }
            );

            const data = await response.json();

            if (response.ok) {
              resultDiv.innerHTML = `
                        <p class="success">Success: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            } else {
              resultDiv.innerHTML = `
                        <p class="error">Error: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            }
          } catch (error) {
            resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
          }
        });

      document
        .getElementById('updatePricesBtn')
        .addEventListener('click', async function () {
          const form = document.getElementById('pricesForm');
          const formData = new FormData(form);
          const packageId = formData.get('id');
          const resultDiv = document.getElementById('result');

          if (!packageId) {
            resultDiv.innerHTML =
              '<p class="error">Please enter Package ID</p>';
            return;
          }

          try {
            resultDiv.innerHTML = '<p>Sending request...</p>';

            // Parse prices as JSON if provided
            if (formData.get('prices')) {
              try {
                const prices = JSON.parse(formData.get('prices'));
                formData.set('prices', JSON.stringify(prices));
              } catch (e) {
                resultDiv.innerHTML =
                  '<p class="error">Invalid JSON format for Prices</p>';
                return;
              }
            }

            // Parse pricesToDelete as JSON if provided
            if (formData.get('pricesToDelete')) {
              try {
                const pricesToDelete = JSON.parse(
                  formData.get('pricesToDelete')
                );
                formData.set('pricesToDelete', JSON.stringify(pricesToDelete));
              } catch (e) {
                resultDiv.innerHTML =
                  '<p class="error">Invalid JSON format for Prices to Delete</p>';
                return;
              }
            }

            const response = await fetch(
              `/api/v1/package/update-package-prices/${packageId}`,
              {
                method: 'PATCH',
                body: formData,
              }
            );

            const data = await response.json();

            if (response.ok) {
              resultDiv.innerHTML = `
                        <p class="success">Success: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            } else {
              resultDiv.innerHTML = `
                        <p class="error">Error: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            }
          } catch (error) {
            resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
          }
        });

      document
        .getElementById('updateImageBtn')
        .addEventListener('click', async function () {
          const form = document.getElementById('imageForm');
          const formData = new FormData(form);
          const packageId = formData.get('id');
          const resultDiv = document.getElementById('result');

          if (!packageId) {
            resultDiv.innerHTML =
              '<p class="error">Please enter Package ID</p>';
            return;
          }

          if (!formData.get('image').size) {
            resultDiv.innerHTML = '<p class="error">Please select an image</p>';
            return;
          }

          try {
            resultDiv.innerHTML = '<p>Sending request...</p>';

            const response = await fetch(
              `/api/v1/package/update-package-image/${packageId}`,
              {
                method: 'PATCH',
                body: formData,
              }
            );

            const data = await response.json();

            if (response.ok) {
              resultDiv.innerHTML = `
                        <p class="success">Success: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            } else {
              resultDiv.innerHTML = `
                        <p class="error">Error: ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
            }
          } catch (error) {
            resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
          }
        });
    </script>
  </body>
</html>
