import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { logger } from '../../utils/logger';

// Define system setting keys
export const SYSTEM_SETTINGS = {
  ADMIN_NOTIFICATION_EMAILS: 'admin_notification_emails',
};

// Helper function to clear all settings-related caches
export const clearSettingsCaches = async (): Promise<void> => {
  await deleteCacheByPattern('settings:*');
};

export const settingsService = {
  // Get a system setting by key
  getSettingByKey: async (key: string) => {
    const cacheKey = `settings:${key}`;
    const cachedSetting = await getCache(cacheKey);

    if (cachedSetting) {
      return JSON.parse(cachedSetting as string);
    }

    const setting = await db.systemSettings.findUnique({
      where: { key },
    });

    if (setting) {
      await setCache(cacheKey, JSON.stringify(setting), 60 * 15); // Cache for 15 minutes
    }

    return setting;
  },

  // Get all system settings
  getAllSettings: async (staffId: any) => {
    await staffHasPermission(staffId, PERMISSIONS.SETTINGS_VIEW);

    const cacheKey = 'settings:all';
    const cachedSettings = await getCache(cacheKey);

    if (cachedSettings) {
      return JSON.parse(cachedSettings as string);
    }

    const settings = await db.systemSettings.findMany({
      orderBy: { key: 'asc' },
    });

    await setCache(cacheKey, JSON.stringify(settings), 60 * 5); // Cache for 5 minutes

    return settings;
  },

  // Create a new system setting
  createSetting: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.SETTINGS_CREATE);

    const { key, value, description } = reqBody;

    const existingSetting = await db.systemSettings.findUnique({
      where: { key },
    });

    if (existingSetting) {
      throw new HttpError('Setting with this key already exists', 409);
    }

    const setting = await db.systemSettings.create({
      data: {
        key,
        value,
        description,
        createdBy: staffId,
      },
    });

    await clearSettingsCaches();

    return setting;
  },

  // Update an existing system setting
  updateSetting: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.SETTINGS_EDIT);

    const { id, key, value, description } = reqBody;

    const existingSetting = await db.systemSettings.findUnique({
      where: { id },
    });

    if (!existingSetting) {
      throw new HttpError('Setting not found', 404);
    }

    const updatedSetting = await db.systemSettings.update({
      where: { id },
      data: {
        key,
        value,
        description,
        updatedBy: staffId,
      },
    });

    await clearSettingsCaches();

    return updatedSetting;
  },

  // Delete a system setting
  deleteSetting: async (staffId: any, id: number) => {
    await staffHasPermission(staffId, PERMISSIONS.SETTINGS_DELETE);

    const existingSetting = await db.systemSettings.findUnique({
      where: { id },
    });

    if (!existingSetting) {
      throw new HttpError('Setting not found', 404);
    }

    await db.systemSettings.delete({
      where: { id },
    });

    await clearSettingsCaches();

    return { message: 'Setting deleted successfully' };
  },

  // Get admin notification emails
  getAdminNotificationEmails: async () => {
    const setting = await settingsService.getSettingByKey(
      SYSTEM_SETTINGS.ADMIN_NOTIFICATION_EMAILS
    );

    if (!setting) {
      // Return an empty array if the setting doesn't exist
      return [];
    }

    try {
      // Parse the value as JSON (array of emails)
      return JSON.parse(setting.value);
    } catch (error) {
      logger.error('Error parsing admin notification emails:', error);
      return [];
    }
  },

  // Update admin notification emails
  updateAdminNotificationEmails: async (staffId: any, emails: string[]) => {
    await staffHasPermission(staffId, PERMISSIONS.SETTINGS_EDIT);

    // Validate emails
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    for (const email of emails) {
      if (!emailRegex.test(email)) {
        throw new HttpError(`Invalid email format: ${email}`, 400);
      }
    }

    const existingSetting = await db.systemSettings.findUnique({
      where: { key: SYSTEM_SETTINGS.ADMIN_NOTIFICATION_EMAILS },
    });

    if (existingSetting) {
      // Update existing setting
      await db.systemSettings.update({
        where: { key: SYSTEM_SETTINGS.ADMIN_NOTIFICATION_EMAILS },
        data: {
          value: JSON.stringify(emails),
          updatedBy: staffId,
        },
      });
    } else {
      // Create new setting
      await db.systemSettings.create({
        data: {
          key: SYSTEM_SETTINGS.ADMIN_NOTIFICATION_EMAILS,
          value: JSON.stringify(emails),
          description:
            'Email addresses to notify when a package booking is completed',
          createdBy: staffId,
        },
      });
    }

    await clearSettingsCaches();

    return { emails };
  },
};
