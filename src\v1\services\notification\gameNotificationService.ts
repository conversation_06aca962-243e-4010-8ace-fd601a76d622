import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import { messagingService } from '../messaging';
import { sendToUser, sendToGame, broadcast } from '../socket';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import config from '../../../config/app.config';

const prisma = new PrismaClient();

export interface GameNotificationData {
  type:
    | 'game_created'
    | 'game_started'
    | 'game_completed'
    | 'game_cancelled'
    | 'game_reward'
    | 'game_reminder'
    | 'game_leaderboard_update';
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high';
}

export class GameNotificationService {
  // Notify when a new game is created
  static async notifyGameCreated(
    gameId: string,
    gameTitle: string,
    creatorName: string,
    locationId?: number
  ) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        select: {
          title: true,
          description: true,
          gameType: true,
          startDate: true,
          endDate: true,
          prizePool: true,
          maxParticipants: true,
          entryFee: true,
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      // Get staff members to notify (based on location if specified)
      const whereClause = locationId ? { locationId } : {};
      const staffMembers = await prisma.staff.findMany({
        where: whereClause,
        select: {
          id: true,
          fullName: true,
          email: true,
        },
      });

      const notificationData: GameNotificationData = {
        type: 'game_created',
        title: `New Game: ${gameTitle}`,
        message: `${creatorName} created a new ${game.gameType.toLowerCase()} game with ${game.prizePool} prize pool`,
        data: {
          gameId,
          gameTitle,
          gameType: game.gameType,
          prizePool: game.prizePool,
          startDate: game.startDate,
          endDate: game.endDate,
          creatorName,
        },
        priority: 'medium',
      };

      // Send notifications to all eligible staff
      for (const staff of staffMembers) {
        await this.sendNotification(staff.id, notificationData);
      }

      // Broadcast real-time notification
      broadcast('game_created', {
        gameId,
        gameTitle,
        gameType: game.gameType,
        prizePool: game.prizePool,
        creatorName,
        timestamp: new Date(),
      });

      logger.info(`Game creation notifications sent for game ${gameId}`);
    } catch (error) {
      logger.error('Error sending game creation notifications:', error);
      throw error;
    }
  }

  // Notify when a game starts
  static async notifyGameStarted(gameId: string) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        include: {
          participants: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      const notificationData: GameNotificationData = {
        type: 'game_started',
        title: `Game Started: ${game.title}`,
        message: `The game "${game.title}" has started! Join now to participate.`,
        data: {
          gameId,
          gameTitle: game.title,
          gameType: game.gameType,
          prizePool: game.prizePool,
        },
        priority: 'high',
      };

      // Notify all participants
      for (const participant of game.participants) {
        await this.sendNotification(participant.staff.id, notificationData);

        // Send email notification
        await this.sendGameStartEmail(
          participant.staff,
          game.title,
          game.gameType,
          Number(game.prizePool)
        );
      }

      // Send real-time notification to game participants
      sendToGame(gameId, 'game_started', {
        gameId,
        gameTitle: game.title,
        message: 'The game has started!',
        timestamp: new Date(),
      });

      logger.info(`Game start notifications sent for game ${gameId}`);
    } catch (error) {
      logger.error('Error sending game start notifications:', error);
      throw error;
    }
  }

  // Notify when a game is completed
  static async notifyGameCompleted(gameId: string) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        include: {
          participants: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
            },
          },
          leaderboard: {
            orderBy: { position: 'asc' },
            take: 3,
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      const winners = game.leaderboard.slice(0, 3);

      const notificationData: GameNotificationData = {
        type: 'game_completed',
        title: `Game Completed: ${game.title}`,
        message: `The game "${game.title}" has ended. Check the leaderboard to see the winners!`,
        data: {
          gameId,
          gameTitle: game.title,
          winners: winners.map((w) => ({
            position: w.position,
            name: `${w.staff.fullName}`,
            score: w.score,
          })),
        },
        priority: 'medium',
      };

      // Notify all participants
      for (const participant of game.participants) {
        await this.sendNotification(participant.staff.id, notificationData);
      }

      // Send real-time notification
      sendToGame(gameId, 'game_completed', {
        gameId,
        gameTitle: game.title,
        winners: winners.map((w) => ({
          position: w.position,
          name: `${w.staff.fullName}`,
          score: w.score,
        })),
        timestamp: new Date(),
      });

      logger.info(`Game completion notifications sent for game ${gameId}`);
    } catch (error) {
      logger.error('Error sending game completion notifications:', error);
      throw error;
    }
  }

  // Notify when rewards are distributed
  static async notifyRewardDistributed(
    staffId: number,
    gameTitle: string,
    rewardAmount: number,
    position: number
  ) {
    try {
      const notificationData: GameNotificationData = {
        type: 'game_reward',
        title: `Congratulations! You won a reward!`,
        message: `You finished in position ${position} in "${gameTitle}" and earned ₦${rewardAmount.toLocaleString()}!`,
        data: {
          gameTitle,
          rewardAmount,
          position,
        },
        priority: 'high',
      };

      await this.sendNotification(staffId, notificationData);

      // Send real-time notification
      sendToUser(staffId, 'reward_received', {
        gameTitle,
        rewardAmount,
        position,
        timestamp: new Date(),
      });

      // Send reward email
      await this.sendRewardEmail(staffId, gameTitle, rewardAmount, position);

      logger.info(
        `Reward notification sent to staff ${staffId} for ${rewardAmount}`
      );
    } catch (error) {
      logger.error('Error sending reward notification:', error);
      throw error;
    }
  }

  // Send game reminder notifications
  static async sendGameReminder(
    gameId: string,
    reminderType: 'starting_soon' | 'ending_soon'
  ) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        include: {
          participants: {
            include: {
              staff: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      const isStarting = reminderType === 'starting_soon';
      const timeLeft = isStarting
        ? Math.ceil((game.startDate.getTime() - Date.now()) / (1000 * 60 * 60)) // hours
        : Math.ceil((game.endDate.getTime() - Date.now()) / (1000 * 60 * 60)); // hours

      const notificationData: GameNotificationData = {
        type: 'game_reminder',
        title: isStarting
          ? `Game Starting Soon: ${game.title}`
          : `Game Ending Soon: ${game.title}`,
        message: isStarting
          ? `The game "${game.title}" starts in ${timeLeft} hours!`
          : `The game "${game.title}" ends in ${timeLeft} hours! Submit your answers now!`,
        data: {
          gameId,
          gameTitle: game.title,
          reminderType,
          timeLeft,
        },
        priority: 'medium',
      };

      // Send to participants or all staff (depending on reminder type)
      const recipients = isStarting
        ? await prisma.staff.findMany({ select: { id: true } }) // All staff for starting reminder
        : game.participants.map((p) => ({ id: p.staff.id })); // Only participants for ending reminder

      for (const recipient of recipients) {
        await this.sendNotification(recipient.id, notificationData);
      }

      // Send real-time notification
      const eventName = isStarting ? 'game_starting_soon' : 'game_ending_soon';
      if (isStarting) {
        broadcast(eventName, {
          gameId,
          gameTitle: game.title,
          timeLeft,
          timestamp: new Date(),
        });
      } else {
        sendToGame(gameId, eventName, {
          gameId,
          gameTitle: game.title,
          timeLeft,
          timestamp: new Date(),
        });
      }

      logger.info(
        `Game reminder notifications sent for game ${gameId} (${reminderType})`
      );
    } catch (error) {
      logger.error('Error sending game reminder notifications:', error);
      throw error;
    }
  }

  // Notify leaderboard updates
  static async notifyLeaderboardUpdate(gameId: string, topParticipants: any[]) {
    try {
      const game = await prisma.game.findUnique({
        where: { id: gameId },
        select: { title: true },
      });

      if (!game) {
        throw new Error('Game not found');
      }

      // Send real-time leaderboard update
      sendToGame(gameId, 'leaderboard_update', {
        gameId,
        gameTitle: game.title,
        topParticipants,
        timestamp: new Date(),
      });

      logger.info(`Leaderboard update sent for game ${gameId}`);
    } catch (error) {
      logger.error('Error sending leaderboard update:', error);
      throw error;
    }
  }

  // Core notification sending method
  private static async sendNotification(
    userId: number,
    notificationData: GameNotificationData
  ) {
    try {
      // Send via existing messaging service
      await messagingService.sendNotification(
        userId,
        notificationData.message,
        this.getNotificationType(notificationData.priority || 'low')
      );

      // Store notification in database for persistence
      await prisma.notification.create({
        data: {
          staffId: userId,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          data: notificationData.data
            ? JSON.stringify(notificationData.data)
            : null,
          priority: notificationData.priority || 'low',
          isRead: false,
        },
      });
    } catch (error) {
      logger.error('Error sending game notification:', error);
      throw error;
    }
  }

  // Send game start email
  private static async sendGameStartEmail(
    staff: any,
    gameTitle: string,
    gameType: string,
    prizePool: number
  ) {
    try {
      if (!staff.email) {
        logger.warn(
          `Staff ${staff.id} has no email for game start notification`
        );
        return;
      }

      const mailOptions = {
        from: '"Cedarcrest Hospitals Games" <<EMAIL>>',
        to: staff.email,
        subject: `Game Started: ${gameTitle}`,
        template: 'game-start',
        context: {
          userName: `${staff.fullName}`,
          gameTitle,
          gameType,
          prizePool: prizePool.toLocaleString(),
          dashboardUrl: `${config.ADMIN_DASHBOARD_BASE_URL}/games`,
        },
      };

      await enqueueSendEmailJob(mailOptions);
      logger.info(`Game start email queued for staff ${staff.id}`);
    } catch (error) {
      logger.error('Error sending game start email:', error);
    }
  }

  // Send reward email
  private static async sendRewardEmail(
    staffId: number,
    gameTitle: string,
    rewardAmount: number,
    position: number
  ) {
    try {
      const staff = await prisma.staff.findUnique({
        where: { id: staffId },
        select: {
          fullName: true,
          email: true,
        },
      });

      if (!staff || !staff.email) {
        logger.warn(
          `Staff ${staffId} not found or has no email for reward notification`
        );
        return;
      }

      const mailOptions = {
        from: '"Cedarcrest Hospitals Games" <<EMAIL>>',
        to: staff.email,
        subject: `Congratulations! You won ₦${rewardAmount.toLocaleString()}!`,
        template: 'game-reward',
        context: {
          userName: `${staff.fullName}`,
          gameTitle,
          rewardAmount: rewardAmount.toLocaleString(),
          position,
          dashboardUrl: `${config.ADMIN_DASHBOARD_BASE_URL}/games`,
        },
      };

      await enqueueSendEmailJob(mailOptions);
      logger.info(`Reward email queued for staff ${staffId}`);
    } catch (error) {
      logger.error('Error sending reward email:', error);
    }
  }

  // Helper method to convert priority to notification type
  private static getNotificationType(
    priority: string
  ): 'info' | 'success' | 'warning' | 'error' {
    switch (priority) {
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
      default:
        return 'info';
    }
  }
}
