import express, { Application } from 'express';
import { createServer, Server } from 'http';
import { logger } from '../v1/utils/logger';
import config from '../config/app.config';

/**
 * Initialize and start the HTTP server
 * @param app Express application instance
 * @returns HTTP server instance
 */
export const startServer = (app: Application): Server => {
  const PORT = Number(config.APPLICATION_PORT) || 8080;
  const httpServer = createServer(app);

  const server = httpServer.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
    logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  });

  return server;
};

/**
 * Create and configure the Express application
 * @returns Configured Express application
 */
export const createApp = (): Application => {
  return express();
};
