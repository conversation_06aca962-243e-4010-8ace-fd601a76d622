import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { formatString } from '../../utils/stringFormatter';
import { createDateFilter } from '../../utils/util';
import { logger } from '../../utils/logger';

/**
 * Utility function to deactivate expired price modifiers
 * This can be called manually or by the cron job
 */
export const deactivateExpiredPriceModifiers = async (): Promise<number> => {
  try {
    // Get current date at the start of the day (midnight)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find all active price modifiers with an endDate that has passed
    const expiredModifiers = await db.packagePriceModifier.findMany({
      where: {
        isActive: true,
        endDate: {
          lt: today, // less than today (i.e., in the past)
        },
      },
      select: {
        id: true,
        modifierCode: true,
        endDate: true,
      },
    });

    if (expiredModifiers.length === 0) {
      return 0;
    }

    // Update all expired modifiers to set isActive to false
    const updateResult = await db.packagePriceModifier.updateMany({
      where: {
        id: {
          in: expiredModifiers.map((modifier) => modifier.id),
        },
      },
      data: {
        isActive: false,
      },
    });

    return updateResult.count;
  } catch (error) {
    logger.error('Error deactivating expired price modifiers:', error);
    throw error;
  }
};

export const priceModifierService = {
  verifyPriceModifier: async (reqBody: any) => {
    const { id, modifierCode } = reqBody;

    // Fetch the packageLocationPrice by ID and include its modifiers
    const pkg = await db.packageLocationPrice.findUnique({
      where: { id: Number(id) },
      include: {
        modifiers: true,
      },
    });

    if (!pkg) {
      throw new HttpError('Package price not found', 400);
    }

    // Find the matching modifier
    const modifier = pkg.modifiers.find(
      (mod) => mod.modifierCode === modifierCode
    );

    if (!modifier) {
      throw new HttpError(
        'This discount code is not valid for the selected location.',
        400
      );
    }

    return {
      id: pkg.id,
      modifier,
    };
  },

  createPriceModifier: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);
    const {
      code,
      startDate,
      endDate,
      amount,
      percentage,
      description,
      packages,
    } = reqBody;
    const formatCode = formatString.formatUpperCase(code);
    const codeExist = await db.packagePriceModifier.findUnique({
      where: {
        modifierCode: formatCode,
      },
    });

    if (codeExist) {
      throw new HttpError('Discount code already exist', 400);
    }

    await db.packagePriceModifier.create({
      data: {
        modifierCode: formatCode,
        amount,
        startDate,
        endDate,
        packageLocationPrice: {
          connect: packages.map((id: any) => ({ id: Number(id) })),
        },
        percentage,
        description,
      },
    });

    return { message: 'Discount created successfully' };
  },

  getAllDiscounts: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    // Parse pagination parameters with defaults
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;

    // Parse filter parameters
    const code = query.code as string | undefined;
    const status = query.status as string | undefined;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const discountStatus =
      status === 'true' ? true : status === 'false' ? false : undefined;

    // Build where clause
    const whereClause: any = {};

    // Add code filter if provided
    if (code) {
      whereClause.modifierCode = code;
    }

    // Add status filter if provided
    if (discountStatus !== undefined) {
      whereClause.isActive = discountStatus;
    }

    // Add date filter using the reusable function
    const dateFilter = createDateFilter(startDate, endDate);

    // Merge all filters
    Object.assign(whereClause, dateFilter);

    const [discounts, totalPages, totalCount] = await db.$transaction([
      db.packagePriceModifier.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          packageLocationPrice: {
            select: {
              id: true,
              amount: true,
              package: {
                select: {
                  name: true,
                  totalSlot: true,
                },
              },
              location: {
                select: {
                  name: true,
                  region: true,
                },
              },
            },
          },
          _count: {
            select: {
              packageLocationPrice: true,
            },
          },
        },
      }),
      db.packagePriceModifier.count({
        where: whereClause,
      }),
      db.packagePriceModifier.count(),
    ]);

    const discountWithPriceCount = discounts.map((discount) => ({
      ...discount,
      packagePricesCount: discount._count.packageLocationPrice,
    }));

    return {
      discounts: discountWithPriceCount,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
      filters: {
        code: code || null,
        status: discountStatus !== undefined ? discountStatus : null,
        dateRange:
          startDate || endDate
            ? {
                startDate: startDate || null,
                endDate: endDate || null,
              }
            : null,
      },
    };
  },

  getAllDiscountRecords: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    // Parse pagination parameters with defaults
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;

    // Parse filter parameters
    const code = query.code as string | undefined;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;

    // Build where clause
    const whereClause: any = {};

    // Add code filter if provided
    if (code) {
      whereClause.code = code;
    }

    // Add date filter using the reusable function
    const dateFilter = createDateFilter(startDate, endDate);

    // Merge all filters
    Object.assign(whereClause, dateFilter);

    const [discounts, totalPages, totalCount] = await db.$transaction([
      db.discountRecord.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              emailAddress: true,
              phoneNumber: true,
            },
          },
        },
      }),
      db.discountRecord.count({
        where: whereClause,
      }),
      db.discountRecord.count(),
    ]);

    return {
      result: discounts,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
      filters: {
        code: code || null,
        dateRange:
          startDate || endDate
            ? {
                startDate: startDate || null,
                endDate: endDate || null,
              }
            : null,
      },
    };
  },

  deactivateExpiredModifiers: async (staffId: any) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

    try {
      const deactivatedCount = await deactivateExpiredPriceModifiers();

      return {
        message: `Successfully deactivated ${deactivatedCount} expired price modifiers`,
        count: deactivatedCount,
      };
    } catch (error) {
      throw new HttpError('Failed to deactivate expired price modifiers', 500);
    }
  },
};
