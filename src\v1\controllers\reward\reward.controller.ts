import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { staffService } from '../../services/staff';
import { rewardService } from '../../services/reward/reward';

const ListRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.getAllReward, req.query, res, staffId);
};

const UpdateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.updateReward, req.body, res, staffId);
};

const CreateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.createReward, req.body, res, staffId);
};

const VerifyReferralCodeHandler = (req: Request, res: Response) => {
  controllerOperations(
    rewardService.verifyReferralCode,
    undefined,
    res,
    req.body
  );
};

export const rewardControllers = {
  ListRewardHandler,
  UpdateRewardHandler,
  CreateRewardHandler,
  VerifyReferralCodeHandler,
};
