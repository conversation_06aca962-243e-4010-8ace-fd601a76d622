import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';

export const specialtyService = {
  getAllSpecialty: async (staffId: any, query: any) => {
    return db.specialty.findMany();
  },

  createSpecialty: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
    const { name } = reqBody;
    const formattedString = formatString.trimString(name);

    const checkSpecialty = await db.specialty.findFirst({
      where: { name: formattedString },
    });

    if (checkSpecialty) {
      throw new HttpError('Specialty already exists', 400);
    }
    await db.specialty.create({
      data: {
        name: formattedString,
      },
    });
    return {
      message: 'Specialty created successfully',
    };
  },
};
