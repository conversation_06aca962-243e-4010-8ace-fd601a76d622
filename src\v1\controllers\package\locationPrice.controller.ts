import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { locationPriceService } from '../../services/package/locationPrice';

const ListLocationPriceHandler = (req: Request, res: Response) => {
  controllerOperations(
    locationPriceService.getAllPackageLocationPrice,
    undefined,
    res
  );
};

const UpdatePackagePriceHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    locationPriceService.updatePackageLocationPrices,
    req.body,
    res,
    staffId
  );
};

export const locationPriceControllers = {
  ListLocationPriceHandler,
  UpdatePackagePriceHandler,
};
