import { Router } from 'express';
import { messagingService } from '../../services/messaging';
import { secure } from '../../middleware/auth';

const router = Router();

router.get('/notifications', secure, async (req: any, res) => {
  const notifications = await messagingService.getOfflineNotifications(
    req.Staff
  );
  res.json({ notifications });
});

router.post('/notify', secure, async (req: any, res) => {
  const { userId, message, type } = req.body;
  await messagingService.sendNotification(userId, message, type);
  res.json({ success: true });
});

router.post('/announce', secure, async (req: any, res) => {
  const { message, type } = req.body;
  await messagingService.broadcastAnnouncement(message, type);
  res.json({ success: true });
});

export default router;
