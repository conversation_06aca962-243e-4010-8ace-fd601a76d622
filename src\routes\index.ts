import { Application, Router } from 'express';
import { routes as v1Routes } from '../v1/routes';
import {
  successResponse,
  errorResponse,
  globalErrorResponse,
} from '../v1/middleware/response';

/**
 * Configure and apply all API routes to the Express application
 * @param app Express application instance
 */
export const setupRoutes = (app: Application): void => {
  const apiRouter = Router();

  // Apply response middleware
  apiRouter.use(successResponse);
  apiRouter.use(errorResponse);
  apiRouter.use(globalErrorResponse);

  // Apply v1 routes
  apiRouter.use('/v1', v1Routes);

  // Mount API router
  app.use('/api', apiRouter);
};
