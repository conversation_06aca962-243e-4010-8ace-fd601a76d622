import dotenv from 'dotenv';
dotenv.config();

const getEnv = (key: string): string => {
  const value = process.env[key];

  // Special handling for NODE_ENV
  if (key === 'NODE_ENV' && value === undefined) {
    return 'development'; // Default to development if not set
  }

  if (value === undefined) {
    throw new Error(`Environment variable ${key} is not set`);
  }
  return value;
};

export default {
  APPLICATION_PORT: getEnv('PORT'),
  SIGNING_TOKEN_SECRET: getEnv('TOKEN_SECRET'),
  CLOUDINARY_CLOUD_NAME: getEnv('CLOUD_NAME'),
  CLOUDINARY_API_KEY: getEnv('CLOUDINARY_KEY'),
  CLOUDINARY_API_SECRET: getEnv('CLOUDINARY_SECRET'),
  CORS_ORIGINS: getEnv('CORS_ORIGINS'),
  PACKAGE_BASE_URL: getEnv('PACKAGE_BASE_URL'),
  REFERRAL_BASE_URL: getEnv('REFERRAL_BASE_URL'),
  ADMIN_DASHBOARD_BASE_URL: getEnv('ADMIN_DASHBOARD_BASE_URL'),
  NODEMAILER_EMAIL: getEnv('EMAIL_ADDRESS'),
  NODEMAILER_EMAIL_PASSWORD: getEnv('EMAIL_PASSWORD'),
  REDIS_DATABASE_URL: getEnv('REDIS_URL'),
  NODE_ENV: getEnv('NODE_ENV'),
};
