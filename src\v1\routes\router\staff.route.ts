import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { staffControllers } from '../../controllers/staff/staff.controller';

export const staffRoute = Router();

staffRoute.post('/check', staffControllers.CheckStaffHandler);
staffRoute.post('/forgot-password', staffControllers.ForgotPasswordHandler);
staffRoute.post('/login', staffControllers.LoginStaffHandler);

staffRoute.post('/add-new', secure, staffControllers.CreateStaffHandler);
staffRoute.post(
  '/add-specialty',
  secure,
  staffControllers.CreateSpecialtyHandler
);

staffRoute.post(
  '/create-department',
  secure,
  staffControllers.CreateDepartmentHandler
);
staffRoute.post('/create-unit', secure, staffControllers.CreateUnitHandler);

staffRoute.post('/generate-link', staffControllers.VerifyStaffCodeHandler);

staffRoute.get('/list', secure, staffControllers.ListStaffHandler);
staffRoute.get(
  '/list-deparment',
  secure,
  staffControllers.ListDepartmentHandler
);
staffRoute.get('/profile', secure, staffControllers.StaffProfile);
staffRoute.get('/list-specialty', staffControllers.ListSpecialtyHandler);
staffRoute.get('/list-consultant', staffControllers.ListConsultantsHandler);

staffRoute.patch('/update', secure, staffControllers.UpdateStaffHandler);
staffRoute.patch(
  '/update-department',
  secure,
  staffControllers.UpdateDepartmentHandler
);
