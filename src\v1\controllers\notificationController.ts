import { Request, Response } from 'express';
import {
  NotificationService,
  NotificationFilters,
} from '../services/notification/notificationService';
import { logger } from '../utils/logger';
import { HttpError } from '../utils/httpError';

export class NotificationController {
  // Get user notifications with pagination and filters
  static async getNotifications(req: any, res: Response) {
    try {
      const userId = req.Staff;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const filters: NotificationFilters = {};

      if (req.query.type) {
        filters.type = req.query.type as string;
      }

      if (req.query.priority) {
        filters.priority = req.query.priority as 'low' | 'medium' | 'high';
      }

      if (req.query.isRead !== undefined) {
        filters.isRead = req.query.isRead === 'true';
      }

      if (req.query.startDate) {
        filters.startDate = new Date(req.query.startDate as string);
      }

      if (req.query.endDate) {
        filters.endDate = new Date(req.query.endDate as string);
      }

      const result = await NotificationService.getUserNotifications(
        userId,
        page,
        limit,
        filters
      );

      res.json({
        success: true,
        message: 'Notifications retrieved successfully',
        data: result,
      });
    } catch (error) {
      logger.error('Error in getNotifications:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notifications',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Mark notifications as read
  static async markAsRead(req: any, res: Response) {
    try {
      const userId = req.Staff;
      const { notificationIds } = req.body;

      if (!notificationIds || !Array.isArray(notificationIds)) {
        throw new HttpError(
          'Notification IDs are required and must be an array',
          400
        );
      }

      const result = await NotificationService.markAsRead(
        userId,
        notificationIds
      );

      res.json({
        success: true,
        message: 'Notifications marked as read successfully',
        data: { updatedCount: result.count },
      });
    } catch (error) {
      logger.error('Error in markAsRead:', error);
      const statusCode = error instanceof HttpError ? error.statusCode : 500;
      res.status(statusCode).json({
        success: false,
        message: 'Failed to mark notifications as read',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Mark all notifications as read
  static async markAllAsRead(req: any, res: Response) {
    try {
      const userId = req.Staff;

      const result = await NotificationService.markAllAsRead(userId);

      res.json({
        success: true,
        message: 'All notifications marked as read successfully',
        data: { updatedCount: result.count },
      });
    } catch (error) {
      logger.error('Error in markAllAsRead:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark all notifications as read',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get unread notification count
  static async getUnreadCount(req: any, res: Response) {
    try {
      const userId = req.Staff;

      const result = await NotificationService.getUnreadCount(userId);

      res.json({
        success: true,
        message: 'Unread count retrieved successfully',
        data: result,
      });
    } catch (error) {
      logger.error('Error in getUnreadCount:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve unread count',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Delete notifications
  static async deleteNotifications(req: any, res: Response) {
    try {
      const userId = req.Staff;
      const { notificationIds } = req.body;

      if (!notificationIds || !Array.isArray(notificationIds)) {
        throw new HttpError(
          'Notification IDs are required and must be an array',
          400
        );
      }

      const result = await NotificationService.deleteNotifications(
        userId,
        notificationIds
      );

      res.json({
        success: true,
        message: 'Notifications deleted successfully',
        data: { deletedCount: result.count },
      });
    } catch (error) {
      logger.error('Error in deleteNotifications:', error);
      const statusCode = error instanceof HttpError ? error.statusCode : 500;
      res.status(statusCode).json({
        success: false,
        message: 'Failed to delete notifications',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get notification preferences
  static async getPreferences(req: any, res: Response) {
    try {
      const userId = req.Staff;

      const preferences =
        await NotificationService.getNotificationPreferences(userId);

      res.json({
        success: true,
        message: 'Notification preferences retrieved successfully',
        data: preferences,
      });
    } catch (error) {
      logger.error('Error in getPreferences:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notification preferences',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Update notification preferences
  static async updatePreferences(req: any, res: Response) {
    try {
      const userId = req.Staff;
      const preferences = req.body;

      // Validate preferences
      const validKeys = [
        'emailNotifications',
        'pushNotifications',
        'forumNotifications',
        'gameNotifications',
        'mentionNotifications',
      ];

      const filteredPreferences: any = {};
      for (const key of validKeys) {
        if (key in preferences && typeof preferences[key] === 'boolean') {
          filteredPreferences[key] = preferences[key];
        }
      }

      if (Object.keys(filteredPreferences).length === 0) {
        throw new HttpError('No valid preferences provided', 400);
      }

      const result = await NotificationService.updateNotificationPreferences(
        userId,
        filteredPreferences
      );

      res.json({
        success: true,
        message: 'Notification preferences updated successfully',
        data: result,
      });
    } catch (error) {
      logger.error('Error in updatePreferences:', error);
      const statusCode = error instanceof HttpError ? error.statusCode : 500;
      res.status(statusCode).json({
        success: false,
        message: 'Failed to update notification preferences',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get notification statistics
  static async getStats(req: any, res: Response) {
    try {
      const userId = req.Staff;

      const stats = await NotificationService.getNotificationStats(userId);

      res.json({
        success: true,
        message: 'Notification statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      logger.error('Error in getStats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notification statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get recent activity
  static async getRecentActivity(req: any, res: Response) {
    try {
      const userId = req.Staff;
      const limit = parseInt(req.query.limit as string) || 10;

      const activity = await NotificationService.getRecentActivity(
        userId,
        limit
      );

      res.json({
        success: true,
        message: 'Recent activity retrieved successfully',
        data: activity,
      });
    } catch (error) {
      logger.error('Error in getRecentActivity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve recent activity',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Send bulk notification (admin only)
  static async sendBulkNotification(req: any, res: Response) {
    try {
      const { userIds, title, message, type, priority } = req.body;

      if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
        throw new HttpError(
          'User IDs are required and must be a non-empty array',
          400
        );
      }

      if (!title || !message) {
        throw new HttpError('Title and message are required', 400);
      }

      const result = await NotificationService.sendBulkNotification(
        userIds,
        title,
        message,
        type || 'system',
        priority || 'medium'
      );

      res.json({
        success: true,
        message: 'Bulk notification sent successfully',
        data: { sentCount: result.count },
      });
    } catch (error) {
      logger.error('Error in sendBulkNotification:', error);
      const statusCode = error instanceof HttpError ? error.statusCode : 500;
      res.status(statusCode).json({
        success: false,
        message: 'Failed to send bulk notification',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Clean up old notifications (admin only)
  static async cleanupOldNotifications(req: any, res: Response) {
    try {
      const result = await NotificationService.cleanupOldNotifications();

      res.json({
        success: true,
        message: 'Old notifications cleaned up successfully',
        data: { deletedCount: result.count },
      });
    } catch (error) {
      logger.error('Error in cleanupOldNotifications:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clean up old notifications',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
